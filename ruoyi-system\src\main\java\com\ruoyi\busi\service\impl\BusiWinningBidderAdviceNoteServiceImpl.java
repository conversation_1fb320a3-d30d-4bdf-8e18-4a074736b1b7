package com.ruoyi.busi.service.impl;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itextpdf.barcodes.BarcodeQRCode;
import com.itextpdf.barcodes.qrcode.EncodeHintType;
import com.itextpdf.barcodes.qrcode.ErrorCorrectionLevel;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfPage;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.font.FontProvider;
import com.ruoyi.busi.domain.BusiAttachment;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.domain.BusiWinningBidderAdviceNote;
import com.ruoyi.busi.domain.BusiWinningBidderNotice;
import com.ruoyi.busi.domain.vo.ProjectInfoAndIdsVo;
import com.ruoyi.busi.enums.AuditBusiTypeEnum;
import com.ruoyi.busi.enums.ProcessEnum;
import com.ruoyi.busi.enums.SystemRoleEnum;
import com.ruoyi.busi.mapper.BusiWinningBidderAdviceNoteMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.NumberUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.common.utils.uuid.Seq;
import com.ruoyi.system.service.ISysDictTypeService;
import com.ruoyi.utils.AttachmentUtil;
import freemarker.template.Template;
import org.apache.fop.render.java2d.Java2DRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;
import org.w3c.dom.Document;
//import org.xhtmlrenderer.swing.Java2DRenderer;
//import org.xhtmlrenderer.util.FSImageWriter;

import javax.annotation.Resource;
import javax.xml.parsers.DocumentBuilderFactory;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 成交通知书Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Service
public class BusiWinningBidderAdviceNoteServiceImpl extends ServiceImpl<BusiWinningBidderAdviceNoteMapper, BusiWinningBidderAdviceNote> implements IBusiWinningBidderAdviceNoteService {

    @Resource
    private FreeMarkerConfigurer configurer;
    @Autowired
    private IBusiTenderProjectService busiTenderProjectService;
    @Autowired
    private IBusiWinningBidderNoticeService busiWinningBidderNoticeService;
    @Autowired
    private IBusiAttachmentService busiAttachmentService;
    @Autowired
    private ISysDictTypeService sysDictTypeService;
    @Autowired
    private IBusiAuditProcessService auditProcessService;
    @Autowired
    private AttachmentUtil attachmentUtil;

    /**
     * 查询成交通知书列表
     *
     * @param busiWinningBidderAdviceNote 成交通知书
     * @return 成交通知书
     */
    @Override
    @DataScope(entAlias = "bidder_id")
    public List<BusiWinningBidderAdviceNote> selectList(BusiWinningBidderAdviceNote busiWinningBidderAdviceNote) {
        QueryWrapper<BusiWinningBidderAdviceNote> busiWinningBidderAdviceNoteQueryWrapper = new QueryWrapper<>();
        busiWinningBidderAdviceNoteQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderAdviceNote.getNoteId()),"note_id",busiWinningBidderAdviceNote.getNoteId());
        busiWinningBidderAdviceNoteQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderAdviceNote.getProjectId()),"project_id",busiWinningBidderAdviceNote.getProjectId());
        busiWinningBidderAdviceNoteQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderAdviceNote.getProjectCode()),"project_code",busiWinningBidderAdviceNote.getProjectCode());
        busiWinningBidderAdviceNoteQueryWrapper.like(ObjectUtil.isNotEmpty(busiWinningBidderAdviceNote.getProjectName()),"project_name",busiWinningBidderAdviceNote.getProjectName());
        busiWinningBidderAdviceNoteQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderAdviceNote.getBidderId()),"bidder_id",busiWinningBidderAdviceNote.getBidderId());
        busiWinningBidderAdviceNoteQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderAdviceNote.getBidderCode()),"bidder_code",busiWinningBidderAdviceNote.getBidderCode());
        busiWinningBidderAdviceNoteQueryWrapper.like(ObjectUtil.isNotEmpty(busiWinningBidderAdviceNote.getBidderName()),"bidder_name",busiWinningBidderAdviceNote.getBidderName());

        LoginUser loginUser = SecurityUtils.getLoginUser();
        if(loginUser!=null){
            boolean isScope = false;
            if(busiWinningBidderAdviceNote.getParams().containsKey("isScope") && Boolean.parseBoolean(busiWinningBidderAdviceNote.getParams().get("isScope").toString())){
                isScope = true;
            }
            for(SysRole role : loginUser.getUser().getRoles()){
                if(role.getRoleKey().equals(SystemRoleEnum.PURCHASER.getCode()) || role.getRoleKey().equals(SystemRoleEnum.AGENCY.getCode())){

                    ProjectInfoAndIdsVo projectsByLoginInfo = busiTenderProjectService.getProjectsByLoginInfo(isScope);
                    busiWinningBidderAdviceNoteQueryWrapper.in("project_id",projectsByLoginInfo.getProjectIds());
                }else if(role.getRoleKey().equals(SystemRoleEnum.SUPPLIER.getCode())){
                    busiWinningBidderAdviceNoteQueryWrapper.apply(
                            ObjectUtil.isNotEmpty(busiWinningBidderAdviceNote.getParams().get("dataScope"))
                                    && isScope,
                            busiWinningBidderAdviceNote.getParams().get("dataScope")+""
                    );
                }else if(role.getRoleKey().equals(SystemRoleEnum.ADMIN.getCode()) || role.getRoleKey().equals(SystemRoleEnum.XIAOHE_AI.getCode())){

                }
            }
        }
        busiWinningBidderAdviceNoteQueryWrapper.orderByDesc("create_time");
        return list(busiWinningBidderAdviceNoteQueryWrapper);
    }

    /**
     * 查询成交通知书列表
     *
     * @param adviceNoteId 成交通知书id
     * @return 成交通知书
     */
    @Override
    public BusiWinningBidderAdviceNote selecInfoIncludeAttachments(Long adviceNoteId) {
        BusiWinningBidderAdviceNote adviceNote = getById(adviceNoteId);

        LoginUser loginUser = SecurityUtils.getLoginUser();
        SysUser user = loginUser.getUser();
        List<SysRole> roles = user.getRoles();
        for (SysRole role : roles) {
            if (adviceNote.getBusiState() == 0 && "supplier".equals(role.getRoleKey())) {
                adviceNote.setSubmit(true);
                break;
            }else if(adviceNote.getBusiState() == 1 && "xiaoheAI".equals(role.getRoleKey())) {
                adviceNote.setAudit(true);
                break;
            }
        }

        //给信息添加附件
//        JSONObject map = new JSONObject();
////        List<BusiAttachment> attachments = busiAttachmentService.selectByBusiId(adviceNoteId);
//        List<SysDictData> sysDictDataList = sysDictTypeService.selectDictDataByType("winning_bidder_advice_note_attachment");
//        List<BusiAttachment> attachmentList = busiAttachmentService.selectByBusiId(adviceNoteId);
//        for (SysDictData data : sysDictDataList) {
////            if (adviceNote.getBusiState() < 2 && data.getDictValue().equals("1")) {
////                continue;
////            }
//            StringBuilder dataFile = new StringBuilder();
//            for (BusiAttachment attachment : attachmentList) {
//                if (attachment.getFileType().equals(data.getDictValue())) {
//                    dataFile.append(",").append(attachment.getFilePath());
//                }
//            }
//            if (dataFile.length() > 0) {
//                map.put(data.getDictLabel(), dataFile.substring(1));
//            }else{
//                map.put(data.getDictLabel(), "");
//            }
//        }
//        adviceNote.setAttachmentMap(map);
        adviceNote.setAttachmentMap(attachmentUtil.getAttachmentMap(adviceNoteId, ProcessEnum.WINNING_BIDDER_ADVICE_NOTE.getAttachmentCode()));
        return adviceNote;
    }

    @Transactional
    @Override
    public boolean audit(BusiWinningBidderAdviceNote winningBidderAdviceNote)  {
        if (winningBidderAdviceNote.getBusiState() == 0) {
            winningBidderAdviceNote.setBusiState(1);
            baseMapper.updateById(winningBidderAdviceNote);
            auditProcessService.saveInfo(winningBidderAdviceNote.getNoteId(), AuditBusiTypeEnum.WINNING_BIDDER_ADVICE_NOTE, 1, "提交", "提交", 1);
        }else if(winningBidderAdviceNote.getBusiState() == 1) {
            if (winningBidderAdviceNote.getAuditResult()) {
                winningBidderAdviceNote.setBusiState(10);
                baseMapper.updateById(winningBidderAdviceNote);
                BusiTenderProject byId = busiTenderProjectService.getById(winningBidderAdviceNote.getProjectId());
                if (null!=byId){
                    byId.setProjectStatus(70);
                    busiTenderProjectService.updateById(byId);
                }

                auditProcessService.saveInfo(winningBidderAdviceNote.getNoteId(), AuditBusiTypeEnum.WINNING_BIDDER_ADVICE_NOTE, 1, "通过", winningBidderAdviceNote.getAuditRemark(), 10);
            }else{
                winningBidderAdviceNote.setBusiState(0);
                baseMapper.updateById(winningBidderAdviceNote);
                auditProcessService.saveInfo(winningBidderAdviceNote.getNoteId(), AuditBusiTypeEnum.WINNING_BIDDER_ADVICE_NOTE, 0, "退回", winningBidderAdviceNote.getAuditRemark(), 0);
            }
        }
        Map<String, Object> attachmentMap = winningBidderAdviceNote.getAttachmentMap();
        if (attachmentMap != null) {
            for (String dictType : attachmentMap.keySet()) {
                if (StringUtils.isNoneBlank(attachmentMap.get(dictType).toString())) {
                    String dictTypeValue = "";
//                    if(dictType.equals("支付凭证")){
                        if(dictType.equals("证明材料")){
                        dictTypeValue ="2";
                    }else if(dictType.equals("其它附件")){
                        dictTypeValue = "3";
                    }
                    busiAttachmentService.deleteByBusiIdAndType(winningBidderAdviceNote.getNoteId(), dictTypeValue);
                    String[] as = attachmentMap.get(dictType).toString().split(",");
                    for (String a : as) {
                        BusiAttachment attachment = new BusiAttachment();
                        attachment.setBusiId(winningBidderAdviceNote.getNoteId());
                        attachment.setFilePath(a);
                        attachment.setFileName(FileUtils.getName(a));
                        String suffix = attachment.getFileName().substring(attachment.getFileName().lastIndexOf("."));
                        attachment.setFileType(dictTypeValue);
                        busiAttachmentService.save(attachment);
                    }
                }
            }
        }
        return true;
    }


    @Override
    public void saveAdviceNote(BusiWinningBidderNotice winningBidderNotice)  {
        BusiTenderProject tenderProject = busiTenderProjectService.getById(winningBidderNotice.getProjectId());
        tenderProject.setProjectStatus(70);
        busiTenderProjectService.updateById(tenderProject);
        BusiWinningBidderAdviceNote winningBidderAdviceNote = new BusiWinningBidderAdviceNote();
        winningBidderAdviceNote.setProjectId(winningBidderNotice.getProjectId());
        winningBidderAdviceNote.setProjectCode(tenderProject.getProjectCode());
        winningBidderAdviceNote.setProjectName(tenderProject.getProjectName());
        winningBidderAdviceNote.setBidderId(winningBidderNotice.getBidderId());
        winningBidderAdviceNote.setBidderCode(winningBidderNotice.getBidderCode());
        winningBidderAdviceNote.setBidderName(winningBidderNotice.getBidderName());
        winningBidderAdviceNote.setBusiState(10);
        baseMapper.insert(winningBidderAdviceNote);
        try {
            saveAttachment(winningBidderAdviceNote, winningBidderNotice, tenderProject);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void saveAttachment(BusiWinningBidderAdviceNote winningBidderAdviceNote, BusiWinningBidderNotice winningBidderNotice, BusiTenderProject tenderProject) throws Exception {
        // 上传文件路径
        String filePath = RuoYiConfig.getUploadPath();
        String fileName = "成交通知书.pdf";
        String fileMainName = "cjtzs";
        fileName = StringUtils.format("{}/{}_{}.{}", DateUtils.datePath(),
                fileMainName, Seq.getId(Seq.uploadSeqType), "pdf");
        String filePathNew = filePath + File.separator + fileName;
        File f = new File(filePathNew);
        if (!f.exists()) {
            if (!f.getParentFile().exists()) {
                f.getParentFile().mkdirs();
            }
        }
        String url = getUrl(fileName);
        change2PDF2(url, f.getAbsolutePath(), winningBidderNotice, tenderProject);
        BusiAttachment attachment = new BusiAttachment();
        attachment.setBusiId(winningBidderAdviceNote.getNoteId());
        attachment.setFilePath(url);
        attachment.setFileSuffix("pdf");
        attachment.setFileName("成交通知书.pdf");
        attachment.setFileType("1");
        busiAttachmentService.save(attachment);

    }

    private String getUrl(String fileName) throws Exception{
        // 上传文件路径
        String filePath = RuoYiConfig.getUploadPath();
        int dirLastIndex = RuoYiConfig.getProfile().length() + 1;
        String currentDir = StringUtils.substring(filePath, dirLastIndex);
        currentDir = Constants.RESOURCE_PREFIX + "/" + currentDir + "/" + fileName;
        return currentDir;
    }

    private File change2PDF2(String url, String filePath, BusiWinningBidderNotice winningBidderNotice, BusiTenderProject tenderProject) throws Exception {

        Map<String, String> map = new HashMap<>();
        map.put("bidderName", winningBidderNotice.getBidderName());
        map.put("tenderName", tenderProject.getTendererName());
        map.put("tenderPhone", tenderProject.getTendererPhone());
        map.put("agentName", org.apache.commons.lang3.StringUtils.isNoneBlank(tenderProject.getAgencyName())?tenderProject.getAgencyName():"");
        map.put("agentPhone", org.apache.commons.lang3.StringUtils.isNoneBlank(tenderProject.getAgencyPhone())?tenderProject.getAgencyPhone():"");
        map.put("projectCode", tenderProject.getProjectCode());
        map.put("projectName", tenderProject.getProjectName());
        map.put("projectTime", DateUtils.getChineseDate(winningBidderNotice.getCreateTime()));
        map.put("createTime", DateUtils.getChineseDate(new Date()));
        map.put("bidderAmount", String.valueOf(winningBidderNotice.getBidAmount().setScale(2, RoundingMode.HALF_UP)));
        map.put("bidderAmountBig", NumberUtil.dealMoney(winningBidderNotice.getBidAmount().setScale(2, RoundingMode.HALF_UP)));

        Template template = configurer.getConfiguration().getTemplate("成交通知书.ftl");
        String res = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
        try{
            File pdfFile = new File(filePath);
            PdfWriter pdfWriter = new PdfWriter(new FileOutputStream(pdfFile));
            PdfDocument pdfDocument = new PdfDocument(pdfWriter);
            pdfDocument.setDefaultPageSize(PageSize.A4.rotate());

            ConverterProperties properties = new ConverterProperties();
            FontProvider fontProvider = new FontProvider();

            String simfang = ResourceUtil.getResource("")+"templates/simfang.ttf";
            PdfFont simfangFont = PdfFontFactory.createFont(simfang, PdfEncodings.IDENTITY_H, false);
            fontProvider.addFont(simfangFont.getFontProgram(), PdfEncodings.IDENTITY_H);
            properties.setFontProvider(fontProvider);

            InputStream inputStream = new ByteArrayInputStream(res.getBytes(StandardCharsets.UTF_8));

            PdfPage pdfPage = pdfDocument.addNewPage();
            PdfCanvas canvas = new PdfCanvas(pdfPage);

            String bjt = ResourceUtil.getResource("")+"templates/11.png";
            Image image = new Image(ImageDataFactory.create(bjt));
            image.setAutoScale(true);
//            float f = (72/300f)*100.0f;
//            image.scale(f, f);
//            image.setFixedPosition(1, 1);
            image.setBackgroundColor(new DeviceRgb(255, 255, 255));
            canvas.addXObject(image.getXObject(), 0, 0);

            // 生成二维码
            Map<EncodeHintType, Object> mHints = new HashMap<>();
            mHints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.Q);
            mHints.put(EncodeHintType.MIN_VERSION_NR, 2);
            //http://localhost/qrcode-result/qrcodeInfo=**********
            String baseUrl = "https://xeyx.hbszfcgxh.com:10443/qrcode-result/"+tenderProject.getProjectId();
            System.out.println("baseUrl:"+baseUrl);
            String s = new String(baseUrl.getBytes(), StandardCharsets.UTF_8);
            BarcodeQRCode qrCode = new BarcodeQRCode(baseUrl, mHints);
            Image qrCodeImage = new Image(qrCode.createFormXObject(ColorConstants.BLACK, 2, pdfDocument));
            qrCodeImage.setWidth(100);
            qrCodeImage.setHeight(100);
            canvas.addXObject(qrCodeImage.getXObject(), 650, 420);

            com.itextpdf.layout.Document document = new com.itextpdf.layout.Document(pdfDocument);

//            document.add(image);
            HtmlConverter.convertToPdf(inputStream, pdfDocument, properties);
            pdfWriter.close();
            pdfDocument.close();
            document.close();
            return pdfFile;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }

//
//    private void change2Image() throws Exception {
//        Map<String, String> map = new HashMap<>();
//        map.put("bidderName", "河南云中鹤大数据产业发展有限公司");
//        map.put("tenderName", "河南省鹤壁市淇滨区鹤壁市妇幼保健院");
//        map.put("agentName", "河南云中鹤大数据产业发展有限公司");
//        map.put("projectCode", "23523FH346346");
//        map.put("projectName", "福田小学10kV增容配电工程（鹤壁市福田小学综合教学楼改建）-高压工程");
//        map.put("projectTime", "2025年05月06日");
//        map.put("bidderAmount", "5000000.00");
//        map.put("bidderAmountBig", "伍佰万元整");
//        Template template = configurer.getConfiguration().getTemplate("成交通知书.ftl");
//        String res = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
//        try{
//            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
//            ByteArrayInputStream bis = new ByteArrayInputStream(res.getBytes(StandardCharsets.UTF_8));
//            Document document =
//                    factory.newDocumentBuilder().parse(bis);
//            Java2DRenderer renderer = new Java2DRenderer(document, 1113, 800);
//            BufferedImage image = renderer.getImage();
//            String filePath = "d:/111.jpg";
//            String inageKey = null;
//            FSImageWriter imageWriter = new FSImageWriter();
//            FileOutputStream fos = new FileOutputStream(new File(filePath));
//            imageWriter.write(image, fos);
//            bis.close();
//            image.flush();
//            fos.flush();
//            fos.close();
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }

    public static void main(String[] args) {

        BigDecimal bidAmount = new BigDecimal(111.11);
        System.out.println(bidAmount.setScale(2, RoundingMode.HALF_UP));
        System.out.println(String.valueOf(bidAmount.setScale(2, RoundingMode.HALF_UP)));
    }
}
