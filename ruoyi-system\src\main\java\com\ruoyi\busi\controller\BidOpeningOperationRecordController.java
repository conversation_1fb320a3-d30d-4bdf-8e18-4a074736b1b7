package com.ruoyi.busi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.busi.domain.BidOpeningOperationRecord;
import com.ruoyi.busi.domain.BusiBidOpening;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 开标操作记录Controller
 *
 * <AUTHOR>
 * @date 2024-07-15
 */
@Api(tags = "开标操作记录管理")
@RestController
@RequestMapping("/operation/record")
public class BidOpeningOperationRecordController extends BaseController {
    @Autowired
    private IBidOpeningOperationRecordService bidOpeningOperationRecordService;
    @Autowired
    private IBusiBiddingRecordService busiBiddingRecordService;
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    private IBusiBidOpeningService iBusiBidOpeningService;
    /**
     * 查询开标操作记录列表
     */
    @PreAuthorize("@ss.hasPermi('operation:record:list')")
    @ApiOperation(value = "查询开标操作记录列表")
    @GetMapping("/list")
    public TableDataInfo list(BidOpeningOperationRecord bidOpeningOperationRecord) {
        startPage();
        List<BidOpeningOperationRecord> list = bidOpeningOperationRecordService.selectList(bidOpeningOperationRecord);
        return getDataTable(list);
    }


    //查询指定项目供应商及代理机构的状态
    @PostMapping("/getProjectStatus")
    public AjaxResult getProjectStatus(@RequestBody  BidOpeningOperationRecord bidOpeningOperationRecord) {
        return bidOpeningOperationRecordService.getProjectStatus(bidOpeningOperationRecord,getLoginUser());
    }

    /**
     * 获取项目供应商签到人数统计
     */
    @ApiOperation(value = "获取项目供应商签到人数统计")
    @PostMapping("/getSignInCount")
    public AjaxResult getSignInCount(@RequestBody BidOpeningOperationRecord bidOpeningOperationRecord) {
        return bidOpeningOperationRecordService.getSignInCount(bidOpeningOperationRecord.getProjectId());
    }





    /**
     * 导出开标操作记录列表
     */
    @PreAuthorize("@ss.hasPermi('operation:record:export')")
    @Log(title = "开标操作记录", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出开标操作记录列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BidOpeningOperationRecord bidOpeningOperationRecord) {
        List<BidOpeningOperationRecord> list = bidOpeningOperationRecordService.selectList(bidOpeningOperationRecord);
        ExcelUtil<BidOpeningOperationRecord> util = new ExcelUtil<BidOpeningOperationRecord>(BidOpeningOperationRecord. class);
        util.exportExcel(response, list, "开标操作记录数据");
    }

    /**
     * 获取开标操作记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('operation:record:query')")
    @ApiOperation(value = "获取开标操作记录详细信息")
    @ApiImplicitParam(name = "operationId", value = "操作ID", required = true, dataType = "Long")
    @GetMapping(value = "/{operationId}")
    public AjaxResult getInfo(@PathVariable("operationId")Long operationId) {
        return success(bidOpeningOperationRecordService.getById(operationId));
    }

    /**
     * 新增开标操作记录
     */
    @PreAuthorize("@ss.hasPermi('operation:record:add')")
    @Log(title = "开标操作记录", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增开标操作记录")
    @PostMapping
    public AjaxResult add(@RequestBody BidOpeningOperationRecord bidOpeningOperationRecord) {
        try {
            return bidOpeningOperationRecordService.saveRecord(bidOpeningOperationRecord);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getCause().getMessage());
        }
    }

    /**
     * 修改开标操作记录
     */
    @PreAuthorize("@ss.hasPermi('operation:record:edit')")
    @Log(title = "开标操作记录", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改开标操作记录")
    @PutMapping
    public AjaxResult edit(@RequestBody BidOpeningOperationRecord bidOpeningOperationRecord) {
        return toAjax(bidOpeningOperationRecordService.updateById(bidOpeningOperationRecord));
    }

    /**
     * 删除开标操作记录
     */
    @PreAuthorize("@ss.hasPermi('operation:record:remove')")
    @Log(title = "开标操作记录", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除开标操作记录")
    @DeleteMapping("/{operationIds}")
    public AjaxResult remove(@PathVariable Long[] operationIds) {
        return toAjax(bidOpeningOperationRecordService.removeByIds(Arrays.asList(operationIds)));
    }
}
