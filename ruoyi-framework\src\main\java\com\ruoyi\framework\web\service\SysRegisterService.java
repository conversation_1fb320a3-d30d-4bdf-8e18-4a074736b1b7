package com.ruoyi.framework.web.service;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.utils.AttachmentUtil;
import io.swagger.models.auth.In;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.RegisterBody;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.user.CaptchaException;
import com.ruoyi.common.exception.user.CaptchaExpireException;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;

/**
 * 注册校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysRegisterService {
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private IBaseEntInfoService baseEntInfoService;
    @Value("${extractcode.thirdPartySecret}")
    private String thirdPartySecret;


    /**
     * 注册
     */
    @Transactional
    public AjaxResult register(RegisterBody registerBody) {
        String msg = "", username = registerBody.getUsername(), password = registerBody.getPassword();
        SysUser sysUser = new SysUser();
        if (registerBody.getUserId() != null) {
            sysUser = userService.selectUserById(registerBody.getUserId());
        }
        sysUser.setUserName(username);
        // 验证码开关
        boolean captchaEnabled = configService.selectCaptchaEnabled();
        if (captchaEnabled && !thirdPartySecret.equals(registerBody.getThirdPartySecret())) {
            validateCaptcha(username, registerBody.getCode(), registerBody.getUuid());
        }

        if (StringUtils.isEmpty(username)) {
            msg = "用户名不能为空";
        } else if (StringUtils.isEmpty(password)) {
            msg = "用户密码不能为空";
        } else if (!thirdPartySecret.equals(registerBody.getThirdPartySecret())
                && (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH)) {
            msg = "账户长度必须在2到20个字符之间";
        } else if (!thirdPartySecret.equals(registerBody.getThirdPartySecret())
                && (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH)) {
            msg = "密码长度必须在5到20个字符之间";
        } else if (!userService.checkUserNameUnique(sysUser)) {
           /* System.out.println("--"+sysUser.getUserName());
            //1采购单位2代理机构3供应商
            SysUser byUserName = userService.selectUserByUserName(sysUser.getUserName());

            if (null!=byUserName){
                System.out.println(JSONObject.toJSONString(byUserName));
                BaseEntInfo baseEntInfo = baseEntInfoService.getById(byUserName.getEntId());
                baseEntInfo.setEntName(registerBody.getEntName());
                baseEntInfo.setEntCode(registerBody.getEntCode());
                baseEntInfo.setEntType(registerBody.getEntType());
                baseEntInfo.setEntAddress(registerBody.getEntAddress());
                baseEntInfo.setEntKeyAgencyAreas(registerBody.getEntKeyAgencyAreas());
               // baseEntInfo.setEntStatus(0);
                baseEntInfo.setBusiState(10);
                baseEntInfo.setEntContactPhone(registerBody.getEntContactPhone());
                //--测试营业执照复制文件
                //  String targetDir="/home/<USER>/uploadPath";
                String copiedFilePath = copyFileFromUrl(registerBody.getBusinessLicense(), RuoYiConfig.getProfile()+"/upload" );
                String yyzzString = AttachmentUtil.realToUrl(copiedFilePath);
                baseEntInfo.setBusinessLicense(yyzzString);
                baseEntInfo.setEntLegalPerson(registerBody.getEntLegalPerson());
                baseEntInfo.setEntLegalPersonPhone(registerBody.getEntLegalPersonPhone());
                //--测试身份证复制文件
                String personCardFilePath = copyFileFromUrl(registerBody.getLegalPersonCardFile(), RuoYiConfig.getProfile()+"/upload" );
                String personCardString = AttachmentUtil.realToUrl(personCardFilePath);
                baseEntInfo.setEntLegalPersonCardFile(personCardString);
                baseEntInfo.setEntLinkman(registerBody.getEntLinkman());
                baseEntInfoService.saveOrUpdate(baseEntInfo);
            }else {
                System.out.println("---22--");
            }
*/
            msg = "保存用户'" + username + "'失败，注册账号已存在";
        } else {
            sysUser.setNickName(username);
            sysUser.setPassword(SecurityUtils.encryptPassword(password));
            sysUser.getParams().put("opUser", registerBody.getOpUser());
            sysUser.setRemark(registerBody.getType());
            boolean regFlag = userService.registerUser(sysUser);
            if (!regFlag) {
                msg = "注册失败,请联系系统管理人员";
            } else {
                Long[] roleIds = new Long[1];
                BaseEntInfo baseEntInfo=new BaseEntInfo();
                if (StringUtils.isNotBlank(registerBody.getType())) {
//                    if (registerBody.getType().equals("expert")) {
//                        //查询专家权限
//                        //根据企业类型获取角色
//                        String roleId = configService.selectConfigByKey("sys:role:expert");
//                        roleIds[0] = Long.parseLong(roleId);
//                        baseEntInfo= createBaseEnt(registerBody, sysUser);
//                    } else
                        if (registerBody.getType().equals("zcxh")) {
                        baseEntInfo=createBaseEnt(registerBody, sysUser);
                        //开通企业权限
                        Map<Integer, String> roleMap = new HashMap<>();
                        //企业类型，1采购单位2代理机构3供应商
                        roleMap.put(0, "sys:role:expert");
                        roleMap.put(1, "sys:role:purchaser");
                        roleMap.put(2, "sys:role:agency");
                        roleMap.put(3, "sys:role:supplier");
                        //根据企业类型获取角色
                        String roleId = configService.selectConfigByKey(roleMap.get(registerBody.getEntType()));
                        roleIds[0] = Long.parseLong(roleId);
                    }
                }else{
                    createBaseEnt(registerBody, sysUser);
                    //开通企业权限
                    Map<Integer, String> roleMap = new HashMap<>();
                    //企业类型，1采购单位2代理机构3供应商
                    roleMap.put(0, "sys:role:expert");
                    roleMap.put(1, "sys:role:purchaser");
                    roleMap.put(2, "sys:role:agency");
                    roleMap.put(3, "sys:role:supplier");
                    //根据企业类型获取角色
                    String roleId = configService.selectConfigByKey(roleMap.get(registerBody.getEntType()));
                    roleIds[0] = Long.parseLong(roleId);
                }
                //绑定企业
                sysUser.setDeptId(100L);
                sysUser.setRoleIds(roleIds);
                sysUser.setEntId(baseEntInfo.getEntId());
                userService.updateUser(sysUser);
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.REGISTER, MessageUtils.message("user.register.success")));
            }
        }
        AjaxResult ajaxResult = null;
        if (StringUtils.isEmpty(msg)) {
            ajaxResult = AjaxResult.success();
            ajaxResult.put("user", sysUser);
        } else {
            ajaxResult = AjaxResult.error(msg);
        }
        return ajaxResult;
    }

    private BaseEntInfo createBaseEnt(RegisterBody registerBody, SysUser sysUser) {
        //注册成功后创建企业信息
        BaseEntInfo baseEntInfo = new BaseEntInfo();
        if (sysUser.getEntId() != null) {
            baseEntInfo = baseEntInfoService.getById(sysUser.getEntId());
        }
        baseEntInfo.setEntName(registerBody.getEntName());
        baseEntInfo.setEntCode(registerBody.getEntCode());
        baseEntInfo.setEntType(registerBody.getEntType());
        baseEntInfo.setEntAddress(registerBody.getEntAddress());
        baseEntInfo.setEntKeyAgencyAreas(registerBody.getEntKeyAgencyAreas());
        baseEntInfo.setEntStatus(0);
        baseEntInfo.setBusiState(10);

        baseEntInfo.setEntContactPhone(registerBody.getEntContactPhone());

        //--测试营业执照复制文件
      //  String targetDir="/home/<USER>/uploadPath";

        String copiedFilePath = copyFileFromUrl(registerBody.getBusinessLicense(), RuoYiConfig.getProfile()+"/upload" );
        String yyzzString = AttachmentUtil.realToUrl(copiedFilePath);
        baseEntInfo.setBusinessLicense(yyzzString);

        baseEntInfo.setEntLegalPerson(registerBody.getEntLegalPerson());
        baseEntInfo.setEntLegalPersonPhone(registerBody.getEntLegalPersonPhone());
        //--测试身份证复制文件
        String personCardFilePath = copyFileFromUrl(registerBody.getLegalPersonCardFile(), RuoYiConfig.getProfile()+"/upload" );
        String personCardString = AttachmentUtil.realToUrl(personCardFilePath);

        baseEntInfo.setEntLegalPersonCardFile(personCardString);
        baseEntInfo.setEntLinkman(registerBody.getEntLinkman());
        baseEntInfo.setEntContactPhone(registerBody.getEntContactPhone());


        baseEntInfo.getParams().put("opUser", "register");
        baseEntInfoService.saveOrUpdate(baseEntInfo);
        return baseEntInfo;
    }

    /**
     * 校验验证码
     *
     * @param username 用户名
     * @param code     验证码
     * @param uuid     唯一标识
     * @return 结果
     */
    public void validateCaptcha(String username, String code, String uuid) {
        String verifyKey = CacheConstants.CAPTCHA_CODE_KEY + StringUtils.nvl(uuid, "");
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null) {
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha)) {
            throw new CaptchaException();
        }
    }


    /**
     * 将传入的URL转换为实际地址，并将对应文件拷贝到指定目录
     *
     * @param url        原始URL，如"https://ip.hbszfcgxh.com:10443/zcxh/businessLicense/2025-07-12/2dd3ab01-9add-4e5a-8394-9a21ba0010e1.pdf"
     * @param targetPath 目标拷贝目录路径，如"/path/to/target"
     * @return 拷贝成功返回true，失败返回false
     */
    public static String copyFileFromUrl(String url, String targetPath) {
        try {
            // 提取URL中域名后的路径部分
            int pathStartIndex = url.indexOf("/zcxh/");
            if (pathStartIndex == -1) {
                System.out.println("URL格式不正确，未找到关键路径标识");
                return "";
            }

            String urlPath = url.substring(pathStartIndex);

            // 构建实际地址
           String actualBase = "/data/nginx/html/upload";

            // windows测试  String actualBase = "D:/ruoyi/uploadPath/upload";
            Path actualFilePath = Paths.get(actualBase, urlPath.substring(1)); // 去除路径开头的"/"

            // 检查实际文件是否存在
            if (!Files.exists(actualFilePath) || Files.isDirectory(actualFilePath)) {
                System.out.println("实际文件不存在: " + actualFilePath);
                return "";
            }

            // 确保目标目录存在
            Path targetDirPath = Paths.get(targetPath);
            Files.createDirectories(targetDirPath);

            // 构建目标文件路径
            Path targetFilePath = targetDirPath.resolve(actualFilePath.getFileName());

            // 拷贝文件（保留文件属性）
            Files.copy(actualFilePath, targetFilePath, StandardCopyOption.REPLACE_EXISTING,
                    StandardCopyOption.COPY_ATTRIBUTES);

            System.out.println("文件拷贝成功: " + actualFilePath + " -> " + targetFilePath);
            return targetFilePath.toString(); // 返回拷贝后的文件完整路径


        } catch (IOException e) {
            System.out.println("处理过程出错: " + e.getMessage());
            e.printStackTrace();
            return "";
        }
    }

/*

    public static void main(String[] args) {
        // 模拟URL（路径与本地创建的文件一致）
        String testUrl = "https://ip.hbszfcgxh.com:10443/zcxh/procurement/1395566021714949/货物磋商采购文件.pdf";        // 本地目标目录
        String targetDir = "D:/ruoyi/";
        // 调用方法测试
        boolean result = copyFileFromUrl(testUrl, targetDir);
        System.out.println("测试结果：" + (result ? "成功" : "失败"));
    }
*/

}
