# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.7
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）D:/ruoyi/uploadPath
  profile: /home/<USER>/xeyx/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
#    active: local
#    active: dev
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 200MB
      # 设置总上传的文件大小
      max-request-size: 500MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: ************
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 10
    # 密码
    password: 123456
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  freemarker:
    cache: false
    settings:
      template_update_delay: 0
    suffix: .ftl
    template-loader-path: classpath:/templates
# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认60分钟）
  expireTime: 60


# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名   根据自己的项目来
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
  global-config:
    enable-sql-runner: true
    db-config:
      id-type: auto
# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*



extractcode:
  username: xeyxcgpt
  password: 123123
  thirdPartySecret: 65a8e27d8879283831b664bd8b7f0ad4
  ssoPassword: Aa123456.abc

 # url: http://************:8080/renren-fast/sys/login
  baseUrl: http://************:8888/renrenfast
  url: http://************:8888/renrenfast/sys/login
  ZhuanJia: http://************:8888/renrenfast/ums/expert/countData?t=1719453877533&category=&pspm=&zt=1&isZzcq=true
  chouQu: http://************:8888/renrenfast/sms/project/performExtraction
  resetExtract: http://************:8888/renrenfast/sms/project/resetExtract
  deleteExtract: http://************:8888/renrenfast/sms/project/deleteExtract
  zcxhLoginUrl: http://************:8888/renrenfast/sys/login
  zhuanJiaInfo: http://************:8888/renrenfast/ums/expert/info/
  EditZhuanJiaInfo: http://************:8888/renrenfast/ums/expert/update
  zcxhGetUserInfo: http://************:8888/renrenfast/sys/getSysuserByUserName
# # url: http://127.0.0.1:8080/renren-fast/sys/login
#  baseUrl: http://127.0.0.1:6689/zcxhapi
#  url: http://127.0.0.1:6689/zcxhapi/sys/login
#  ZhuanJia: http://127.0.0.1:6689/zcxhapi/ums/expert/countData?t=1719453877533&category=&pspm=&zt=1&isZzcq=true
#  chouQu: http://127.0.0.1:6689/zcxhapi/sms/project/performExtraction
#  resetExtract: http://127.0.0.1:6689/zcxhapi/sms/project/resetExtract
#  deleteExtract: http://127.0.0.1:6689/zcxhapi/sms/project/deleteExtract
#  zcxhLoginUrl: http://127.0.0.1:6689/zcxhapi/sys/login
#  zhuanJiaInfo: http://127.0.0.1:6689/zcxhapi/ums/expert/info/
#  EditZhuanJiaInfo: http://127.0.0.1:6689/zcxhapi/ums/expert/update
#  zcxhGetUserInfo: http://127.0.0.1:6689/zcxhapi/sys/getSysuserByUserName

kaifangqian:
  #证书服务地址
  cert-apply-url: https://localhost/service/cert/event
  #授权token
  token: 123456
  # 默认 false 签发本地测试证书 true 签发 CA 证书
  prod: false
