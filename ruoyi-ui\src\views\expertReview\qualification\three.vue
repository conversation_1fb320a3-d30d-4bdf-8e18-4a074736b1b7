<template>
  <div class="three">
    <div style="width:70%">
      <div style="font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px">资格性评审</div>

      <el-table :data="tableData" border style="width: 100%" :header-cell-style="headStyle" :cell-style="cellStyle">
        <el-table-column prop="供应商名称" width="180">
        </el-table-column>
        <el-table-column v-for="(item, index) in columns" :key="index" :prop="item.xm" :label="item.xm">
          <template slot-scope="scope">
	          <span v-if="scope.row[item.xm] == '/'">未提交</span>
            <span v-if="scope.row[item.xm] == '1'">通过</span>
            <span v-if="scope.row[item.xm] == '0'">不通过</span>
            <!-- <i v-else style="color:#176ADB;font-size:20px" :class="getIconClass(scope.row[item.xm])"></i> -->
          </template>
        </el-table-column>
      </el-table>
	    
      <div class="result">
        <div style="font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;margin-bottom:20px">评审结果：</div>
        <div style="display: flex;margin-left:30px">
          <div style="margin-right:30px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;" v-for="(item,index) in (Array.isArray(result) ? result : [])" :key="index">
            {{ item.gys }}：
            <span v-if="item.result" style="color:green">
              通过
            </span>
            <span v-else style="color:red">
              不通过
            </span>
          </div>
        </div>
      </div>
      <div class="operation" v-if="!finish">
        <el-button
          class="item-button"
          style="background-color: #f5f5f5;color: #176adb;"
          @click="reviewed"
        >重新评审</el-button>
        <el-button class="item-button" v-if="passedSupplierCount >= 3" @click="completed">节点评审完成</el-button>
        <el-button class="item-button-red"
                   v-if="!hasIncompleteExpert"
                   :disabled="passedSupplierCount >= 3"
                   :style="passedSupplierCount >= 3 ? 'background-color: #ccc; color: #fff; cursor: not-allowed;' : ''"
                   @click="flowLabel">流标</el-button>
      </div>
      <div v-else class="operation">
        <el-button class="item-button" @click="back">返回</el-button>
      </div>
    </div>
    <div style="width:30%">
      <div class="result">
        <div style="font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px">表决结果</div>
        <el-input disabled class="text" type="textarea" :rows="20" placeholder="请输入表决结果" v-model="votingResults">
        </el-input>
      </div>
    </div>
    <el-dialog title="流标情况说明" :visible.sync="dialogVisible" width="30%">
      <el-input type="textarea" :rows="4" placeholder="请输入内容" v-model="reasonFlowBid">
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmflow">确 定</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
// 导入专家评审相关API接口
import {
  leaderSummaryQuery,  // 组长汇总查询接口
  reEvaluate,         // 重新评审接口
  expertInfoById,     // 根据ID获取专家信息接口
} from "@/api/expert/review";

// 导入评审流程相关API接口
import { updateProcess } from "@/api/evaluation/process";        // 更新评审流程接口
import { abortiveTenderNotice } from "@/api/bidder/notice";      // 流标通知接口
import { reEvaluationTwo } from "@/api/evaluation/expertStatus"; // 重新评审状态接口

export default {
  // 组件属性定义
  props: {
    // 是否完成评审的标识
    finish: {
      type: Boolean,    // 布尔类型
      default: false,   // 默认值为false，表示未完成
    },
  },
  // 组件数据定义
  data() {
    return {
      // 表格数据数组，存储评审表格的行数据
      tableData: [],

      // 表格列配置对象，存储表格列的定义信息
      columns: {},

      // 评审结果数组，存储每个供应商的评审结果
      result: [], // 修改：初始化为数组而不是对象

      // 表决结果文本，存储专家组的表决意见
      votingResults: "",

      // 流标原因，当需要流标时填写的原因
      reasonFlowBid: "",

      // 对话框显示状态控制
      dialogVisible: false,

      // 未评审专家列表，存储尚未完成评审的专家信息
      unreviewedExperts: [],

      // 组长信息对象
      leader: {},

      // 定时器ID，用于清除定时器
      intervalId: null,

      // 表格头部样式配置
      headStyle: {
        "text-align": "center",                    // 文字居中对齐
        "font-family": "SourceHanSansSC-Bold",     // 字体家族
        background: "#176ADB",                     // 背景色（蓝色）
        color: "#fff",                             // 文字颜色（白色）
        "font-size": "16px",                       // 字体大小
        "font-weight": "700",                      // 字体粗细（加粗）
        border: "0",                               // 边框设置
      },

      // 表格单元格样式配置
      cellStyle: {
        "text-align": "center",                    // 文字居中对齐
        "font-family": "SourceHanSansSC-Bold",     // 字体家族
        height: "60px",                            // 单元格高度
        color: "#000",                             // 文字颜色（黑色）
        "font-size": "14px",                       // 字体大小
        "font-weight": "700",                      // 字体粗细（加粗）
      },
    };
  },
  // 组件方法定义
  methods: {
    /**
     * 初始化方法
     * 获取评审数据并处理显示
     */
    init() {
      // 构建请求参数
      const data = {
        projectId: this.$route.query.projectId,           // 项目ID（从路由参数获取）
        itemId: this.$route.query.scoringMethodItemId,    // 评分方法项目ID（从路由参数获取）
      };

      // 调用组长汇总查询接口
      leaderSummaryQuery(data).then((response) => {
        if (response.code == 200) {
          // 请求成功，处理返回数据
	        
          // 设置表决结果文本
          this.votingResults = response.data.bjjgsb;

          // 设置未评审专家列表
          this.unreviewedExperts = response.data.wpszj;

          // 转换并设置表格数据
          this.tableData = this.transformData(
            response.data.tableColumns,    // 表格列配置
            response.data.busiBidderInfos, // 投标人信息
            response.data.tableData        // 原始表格数据
          );

          // 过滤掉已废标的数据（isAbandonedBid == 0 表示未废标）
          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)

          // 设置表格列配置
          this.columns = response.data.tableColumns;

          // 生成评审结果表
          this.result = this.generateResultTable(
            response.data.tableColumns,    // 表格列配置
            response.data.busiBidderInfos, // 投标人信息
            response.data.tableData        // 原始表格数据
          );

          console.log(this.result)

          // 添加安全检查：确保 result 是数组后再进行过滤
          if (Array.isArray(this.result)) {
            // 过滤掉已废标的评审结果
            this.result = this.result.filter(item => item.isAbandonedBid == 0)
          } else {
            console.error("generateResultTable did not return an array:", this.result);
            this.result = []; // 设置为空数组作为后备
          }

          console.log(this.tableData)
	        
        } else {
          // 请求失败，显示警告信息
          this.$message.warning(response.msg);
        }
      });
    },
    /**
     * 数据转换函数
     * 将原始数据转换为表格显示所需的格式
     * @param {Array} tableColumns - 表格列配置数组
     * @param {Array} busiBidderInfos - 投标人信息数组
     * @param {Array} tableData - 原始表格数据数组
     * @returns {Array} 转换后的表格数据
     */
    transformData(tableColumns, busiBidderInfos, tableData) {
      // 创建投标人ID到投标人信息的映射
      // 用于快速查找投标人名称和废标状态
      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {
        acc[info.bidderId] = {
          bidderName: info.bidderName,                    // 投标人名称
          isAbandonedBid: info.isAbandonedBid || 0        // 是否废标（默认为0，表示未废标）
        };
        return acc;
      }, {});
			
      // 创建结果ID到项目名称的映射（虽然当前未使用，但保留以备后用）
      const columnIdToName = tableColumns.reduce((acc, column) => {
        acc[column.resultId] = column.xm;
        return acc;
      }, {});

      // 转换原始数据为表格显示格式
      return tableData.map((row) => {
        const supplierId = row.gys;  // 供应商ID
        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];

        // 初始化转换后的行数据，包含供应商名称和废标状态
        const transformedRow = {
          供应商名称: bidderName,
          isAbandonedBid: isAbandonedBid
        };

        // 遍历表格列配置，将对应的评估结果添加到行数据中
        tableColumns.forEach((column) => {
          const itemId = column.resultId;  // 评估项ID
          // 设置评估结果，如果没有找到对应值则默认为'/'(没有评完)
          transformedRow[column.xm] = row[itemId] || "/";
        });

        return transformedRow;
      });
    },
    /**
     * 组装评审结果方法
     * 根据评审数据生成最终的评审结果，采用少数服从多数的原则
     * @param {Array} tableColumns - 表格列配置数组
     * @param {Array} busiBidderInfos - 投标人信息数组
     * @param {Array} tableData - 原始表格数据数组
     * @returns {Array} 评审结果数组，每个元素包含投标人信息和评审结果
     */
    generateResultTable(tableColumns, busiBidderInfos, tableData) {
      // 添加安全检查：确保输入参数都是数组
      if (!Array.isArray(tableColumns) || !Array.isArray(busiBidderInfos) || !Array.isArray(tableData)) {
        console.error("generateResultTable: Invalid input parameters", {
          tableColumns: tableColumns,
          busiBidderInfos: busiBidderInfos,
          tableData: tableData
        });
        return []; // 返回空数组作为后备
      }

      // 提取所有评估项的ID
      const entMethodItemIds = tableColumns.map((item) => {
        return item.resultId;
      });

      // 创建投标人ID到投标人信息的映射
      // 使用Map数据结构提高查找效率
      const bidderMap = new Map(
        busiBidderInfos.map((bidder) => [bidder.bidderId, {
          bidderName: bidder.bidderName,                    // 投标人名称
          isAbandonedBid: bidder.isAbandonedBid || 0        // 是否废标状态
        }])
      );

      // 生成结果表，按照少数服从多数规则判断是否通过
      return tableData.map((row) => {
        const supplierId = row.gys;  // 获取供应商ID

        // 从映射中获取投标人信息
        const { bidderName, isAbandonedBid } = bidderMap.get(supplierId);

        // 计算评审结果
        const totalItems = entMethodItemIds.length;  // 总评估项数量
        let passedCount = 0;  // 通过的评估项数量

        // 遍历所有评估项，统计通过数量
        entMethodItemIds.forEach((key) => {
          if (row[key] == "1") {  // "1" 表示该评估项通过
            passedCount++;
          }
        });

        // 少数服从多数原则：通过数量 >= 总数的一半（向上取整）则判定为通过
        const result = passedCount >= Math.ceil(totalItems / 2);

        // 返回评审结果对象
        return {
          bidder: supplierId,           // 投标人ID
          gys: bidderName,              // 投标人名称
          isAbandonedBid: isAbandonedBid, // 是否废标
          result: result,               // 评审结果（true: 通过, false: 不通过）
        };
      });
    },
    /**
     * 节点评审完成方法
     * 处理评审完成的逻辑，包括验证和提交评审结果
     */
    completed() {
      // 检查是否有未完成评审的专家
      if (this.unreviewedExperts && this.unreviewedExperts.length > 0){
        // 收集未评审专家的姓名
        let result = [];
        for(let i = 0; i < this.unreviewedExperts.length; i++){
          result.push(this.unreviewedExperts[i].xm)
        }
        // 显示错误提示，列出未完成评审的专家
        this.$message.error(`专家${result.join("、")}未完成评审！`);
        return
      }

      // 二次确认提示
      this.$confirm('是否确认完成节点评审？', '确认完成评审', {
        confirmButtonText: '是',
        cancelButtonText: '否',
        type: 'warning'
      }).then(() => {
        // 所有专家都已完成评审，继续处理评审完成逻辑

        // 从本地存储获取评审流程ID
        const evaluationProcessId = JSON.parse(
          localStorage.getItem("evalProjectEvaluationProcess")
        );

        // 构建更新评审流程的请求数据
        const data = {
          evaluationProcessId: evaluationProcessId.evaluationProcessId,  // 评审流程ID
          evaluationResult: JSON.stringify(this.result),                 // 评审结果（JSON字符串）
          evaluationState: 2,                                            // 评审状态（2表示已完成）
          evaluationResultRemark: this.votingResults,                    // 评审结果备注
        };

        // 调用更新评审流程接口
        updateProcess(data).then((response) => {
          if (response.code == 200) {
            // 更新成功，跳转到专家信息页面
            this.$router.push({
              path: "/expertInfo",
              query: {
                projectId: this.$route.query.projectId,  // 项目ID
                zjhm: this.$route.query.zjhm,            // 专家号码
              },
            });
          } else {
            // 更新失败，显示警告信息
            this.$message.warning(response.msg);
          }
        });
      }).catch(() => {
        this.$message.info('已取消完成评审');
      });
    },

    /**
     * 返回方法
     * 返回到专家信息页面
     */
    back() {
      this.$router.push({
        path: "/expertInfo",
        query: {
          projectId: this.$route.query.projectId,  // 项目ID
          zjhm: this.$route.query.zjhm,            // 专家号码
        },
      });
    },

    /**
     * 获取图标样式类名
     * 根据评审结果值返回对应的图标类名
     * @param {string} value - 评审结果值
     * @returns {string} 图标类名
     */
    getIconClass(value) {
      if (value == "1"){
        return "el-icon-check"           // 通过：显示勾选图标
      }

      if (value == "0"){
        return "el-icon-circle-close"    // 不通过：显示关闭图标
      }

      return value  // 其他情况直接返回原值
    },

    /**
     * 重新评审方法
     * 触发重新评审流程，重置评审状态
     */
    reviewed() {
      // 从本地存储获取专家评分信息，构建查询参数
      const query = {
        projectEvaluationId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).projectEvaluationId,                                          // 项目评审ID
        expertResultId: JSON.parse(localStorage.getItem("evalExpertScoreInfo"))
          .expertResultId,                                              // 专家结果ID
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).scoringMethodItemId,                                          // 评分方法项目ID
      };

      // 调用重新评审接口
      reEvaluationTwo(query).then((res) => {
        if (res.code == 200) {
          // 第一步成功，获取评审流程ID
          const evaluationProcessId = JSON.parse(
            localStorage.getItem("evalProjectEvaluationProcess")
          );

          // 调用重新评审接口
          reEvaluate(evaluationProcessId.evaluationProcessId).then(
            (response) => {
              if (response.code == 200) {
                // 触发重新评审通知，通知其他专家页面
                if (this.$parent && typeof this.$parent.triggerReEvaluationNotification === 'function') {
                  debugger
									this.$parent.triggerReEvaluationNotification();
                }
                // 重新评审成功，发送事件通知父组件切换到第一步
                this.$emit("send", "one");
              } else {
                // 重新评审失败，显示警告信息
                this.$message.warning(response.msg);
              }
            }
          );
        }
      });
    },

    /**
     * 流标方法
     * 显示流标确认对话框
     */
    flowLabel() {
      this.dialogVisible = true;
    },
    /**
     * 确认流标方法
     * 处理流标确认逻辑，发送流标通知
     */
    confirmflow() {
      // 验证流标原因是否填写
      if (this.reasonFlowBid == "") {
        this.$message.warning("请完善情况说明")
        return;
      }

      // 构建流标通知请求数据
      const data = {
        projectId: this.$route.query.projectId,              // 项目ID
        abortiveType: 3,                                     // 流标类型（3表示资格性评审流标）
        remark: this.reasonFlowBid,                          // 流标原因说明
        scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项目ID
      };

      // 调用流标通知接口
      abortiveTenderNotice(data).then((response) => {
        if (response.code == 200) {
          // 流标通知发送成功，跳转到汇总页面
          const query = {
            projectId: this.$route.query.projectId,          // 项目ID
            zjhm: this.$route.query.zjhm,                    // 专家号码
            scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项目ID
          };
          this.$router.push({ path: "/summary", query: query });
        } else {
          // 流标通知发送失败，显示警告信息
          this.$message.warning(response.msg);
        }
      });
    },

    /**
     * 清除定时器的通用方法
     * 在多个生命周期钩子中调用，确保定时器被正确清除
     */
    clearTimer() {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
        console.log("定时器已清除 - qualification/three.vue");
      }
    },
  },

  // 计算属性
  computed:{
    /**
     * 通过评审的供应商数量
     * 统计评审结果中通过的供应商数量
     * @returns {number} 通过评审的供应商数量
     */
    passedSupplierCount() {
      console.log("this.result:",this.result);
      // 添加安全检查：确保 result 是数组
      if (!Array.isArray(this.result)) {
        console.warn("result is not an array:", this.result);
        return 0;
      }
      // 过滤出评审结果为通过的供应商，返回数量
      return this.result.filter(item => item.result).length;
    },

    /**
     * 检查是否有专家未完成评审
     * 遍历tableData检查是否存在"/"状态（未评完）
     * @returns {boolean} true表示有未完成评审的专家，false表示所有专家都已完成评审
     */
    hasIncompleteExpert() {
      // 添加安全检查：确保 tableData 是数组
      if (!Array.isArray(this.tableData)) {
        console.warn("tableData is not an array:", this.tableData);
        return true; // 数据异常时，默认禁用流标按钮
      }

      // 遍历所有供应商的评审数据
      const hasIncomplete = this.tableData.some(row => {
        // 遍历每一行的所有属性，查找是否有"/"状态
        return Object.keys(row).some(key => {
          // 排除供应商名称和废标状态字段，只检查专家评审结果
          if (key !== '供应商名称' && key !== 'isAbandonedBid') {
            return row[key] === '/';
          }
          return false;
        });
      });

      // 输出调试信息
      console.log("hasIncompleteExpert:", hasIncomplete, "tableData:", this.tableData);
      return hasIncomplete;
    }
  },

  // 生命周期钩子
  /**
   * 组件挂载完成后执行
   * 初始化组件数据和定时刷新
   */
  mounted() {
    // 初始化数据
    this.init();

    // 设置定时器，每5秒自动刷新数据
    // 用于实时更新评审状态和结果
    this.intervalId = setInterval(()=>{
      this.init();
    },5000)
  },

  /**
   * 组件销毁前执行
   * 清除定时器，防止内存泄漏
   */
  beforeDestroy() {
    this.clearTimer();
  },

  /**
   * 组件完全销毁后执行
   * 作为额外的安全措施清除定时器
   */
  destroyed() {
    this.clearTimer();
  },

  /**
   * 如果父组件使用了keep-alive，在组件失活时清除定时器
   */
  deactivated() {
    this.clearTimer();
  },
};
</script>

<style lang="scss" scoped>
.three {
  padding: 20px 40px;
  display: flex;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  width: 150px;
  height: 40px;
  margin: 20px 28px;
  color: #fff;
  background-color: #176adb;
  border: 0;
  &:hover {
    color: #fff;
  }
}
.item-button-red {
  width: 150px;
  height: 40px;
  margin: 20px 28px;
  color: #fff;
  background-color: #e92900;
  border: 0;
  &:hover {
    color: #fff;
  }
}
.result {
  text-align: left;
  margin-left: 20px;
}
.operation {
  display: flex;
  justify-content: center;
  align-items: center;
}
.text {
  ::v-deep .el-textarea__inner {
    background-color: #f5f5f5;
    border-radius: 0;
    border: 1px solid #f5f5f5;
  }
}
</style>
