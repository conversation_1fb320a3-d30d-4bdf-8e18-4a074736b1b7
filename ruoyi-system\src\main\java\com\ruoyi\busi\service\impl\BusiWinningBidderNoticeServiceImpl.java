package com.ruoyi.busi.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.domain.BaseTreeData;
import com.ruoyi.base.service.IBaseTreeDataService;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.domain.vo.BusiTenderVo;
import com.ruoyi.busi.domain.vo.ProjectInfoAndIdsVo;
import com.ruoyi.busi.enums.AbortiveTypeEnum;
import com.ruoyi.busi.enums.ProcessEnum;
import com.ruoyi.busi.mapper.BusiWinningBidderNoticeMapper;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.enums.ZcxhEnum;
import com.ruoyi.eval.domain.EvalProjectEvaluationInfo;
import com.ruoyi.eval.domain.EvalProjectEvaluationProcess;
import com.ruoyi.eval.service.IEvalProjectEvaluationInfoService;
import com.ruoyi.eval.service.IEvalProjectEvaluationProcessService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysDictTypeService;
import com.ruoyi.utils.AttachmentUtil;
import com.ruoyi.utils.BaseUtil;
import com.ruoyi.utils.ZcxhUtil;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * 中标结果公告信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Log4j2
@Service
public class BusiWinningBidderNoticeServiceImpl extends ServiceImpl<BusiWinningBidderNoticeMapper, BusiWinningBidderNotice> implements IBusiWinningBidderNoticeService {

    @Autowired
    private IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    private IBusiTenderNoticeService busiTenderNoticeService;
    @Autowired
    private IBusiVenueOccupyService busiVenueOccupyService;
    @Autowired
    private IBusiAttachmentService iBusiAttachmentService;
    @Autowired
    private IBusiWinningBidderAdviceNoteService busiWinningBidderAdviceNoteService;
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IBusiBiddingRecordService iBusiBiddingRecordService;
    @Resource
    private FreeMarkerConfigurer configurer;
    @Autowired
    private ISysDictTypeService dictTypeService;
    @Autowired
    private ISysDictDataService dictDataService;
    @Autowired
    private IBusiProcessInfoService processInfoService;
    @Autowired
    private AttachmentUtil attachmentUtil;
    @Autowired
    private IEvalProjectEvaluationProcessService projectEvaluationProcessService;
    @Autowired
    private IEvalProjectEvaluationInfoService projectEvaluationInfoService;
    @Autowired
    private IBusiExtractExpertResultService busiExtractExpertResultService;
    @Autowired
    private IBaseTreeDataService baseTreeDataService;
    @Autowired
    private IBidOpeningOperationRecordService bidOpeningOperationRecordService;
    @Autowired
    private ZcxhUtil zcxhUtil;

    /**
     * 查询中标结果公告信息列表
     *
     * @param busiWinningBidderNotice 中标结果公告信息
     * @return 中标结果公告信息
     */
    @Override
//    @DataScope(entAlias = "tenderer_id,agency_id")
    public List<BusiWinningBidderNotice> selectList(BusiWinningBidderNotice busiWinningBidderNotice) {
        //查订单
        boolean isScope = false;
        if(!busiWinningBidderNotice.getParams().containsKey("isScope") || Boolean.parseBoolean(busiWinningBidderNotice.getParams().get("isScope").toString())){
            isScope = true;
        }
//        ProjectInfoAndIdsVo projectsByLoginInfo = iBusiTenderProjectService.getProjectsByLoginInfo(isScope);
//        busiWinningBidderNotice.getParams().put("projectIds",projectsByLoginInfo.getProjectIds());
        QueryWrapper<BusiWinningBidderNotice> busiWinningBidderNoticeQueryWrapper = getBusiWinningBidderNoticeQueryWrapper(busiWinningBidderNotice);
        List<BusiWinningBidderNotice> list = list(busiWinningBidderNoticeQueryWrapper);
        if(busiWinningBidderNotice.getParams().containsKey("returnProject")){
            list.forEach(item->{
                item.setProject(iBusiTenderProjectService.getById(item.getProjectId()));
            });
        }
        return list;
    }

    private QueryWrapper<BusiWinningBidderNotice> getBusiWinningBidderNoticeQueryWrapper(BusiWinningBidderNotice busiWinningBidderNotice) {
        QueryWrapper<BusiWinningBidderNotice> busiWinningBidderNoticeQueryWrapper = new QueryWrapper<>();
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getProjectId()), "project_id", busiWinningBidderNotice.getProjectId());
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getNoticeCode()), "notice_code", busiWinningBidderNotice.getNoticeCode());
        busiWinningBidderNoticeQueryWrapper.like(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getNoticeName()), "notice_name", busiWinningBidderNotice.getNoticeName());
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getNoticeContent()), "notice_content", busiWinningBidderNotice.getNoticeContent());
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getNoticeType()), "notice_type", busiWinningBidderNotice.getNoticeType());
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getNoticeStartTime()), "notice_start_time", busiWinningBidderNotice.getNoticeStartTime());
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getBidderId()), "bidder_id", busiWinningBidderNotice.getBidderId());
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getBidderCode()), "bidder_code", busiWinningBidderNotice.getBidderCode());
        busiWinningBidderNoticeQueryWrapper.like(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getBidderName()), "bidder_name", busiWinningBidderNotice.getBidderName());
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getBidAmount()), "bid_amount", busiWinningBidderNotice.getBidAmount());
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getRanking()), "ranking", busiWinningBidderNotice.getRanking());
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getScore()), "score", busiWinningBidderNotice.getScore());
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getDelFlag()), "del_flag", busiWinningBidderNotice.getDelFlag());
        String beginCreateTime = busiWinningBidderNotice.getParams().get("beginCreateTime") != null ? busiWinningBidderNotice.getParams().get("beginCreateTime") + "" : "";
        String endCreateTime = busiWinningBidderNotice.getParams().get("endCreateTime") + "" != null ? busiWinningBidderNotice.getParams().get("endCreateTime") + "" : "";
        busiWinningBidderNoticeQueryWrapper.between(ObjectUtil.isNotEmpty(beginCreateTime) && ObjectUtil.isNotEmpty(endCreateTime), "create_time", beginCreateTime, endCreateTime);
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getCreateBy()), "create_by", busiWinningBidderNotice.getCreateBy());
        String beginUpdateTime = busiWinningBidderNotice.getParams().get("beginUpdateTime") != null ? busiWinningBidderNotice.getParams().get("beginUpdateTime") + "" : "";
        String endUpdateTime = busiWinningBidderNotice.getParams().get("endUpdateTime") + "" != null ? busiWinningBidderNotice.getParams().get("endUpdateTime") + "" : "";
        busiWinningBidderNoticeQueryWrapper.between(ObjectUtil.isNotEmpty(beginUpdateTime) && ObjectUtil.isNotEmpty(endUpdateTime), "update_time", beginUpdateTime, endUpdateTime);
        busiWinningBidderNoticeQueryWrapper.eq(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getUpdateBy()), "update_by", busiWinningBidderNotice.getUpdateBy());
        busiWinningBidderNoticeQueryWrapper.in(ObjectUtil.isNotEmpty(busiWinningBidderNotice.getParams().get("projectIds")),"project_id", (List)busiWinningBidderNotice.getParams().get("projectIds"));
//        busiWinningBidderNoticeQueryWrapper.apply(
//                ObjectUtil.isNotEmpty(busiWinningBidderNotice.getParams().get("dataScope"))
//                        && (!busiWinningBidderNotice.getParams().containsKey("isScope") || Boolean.parseBoolean(busiWinningBidderNotice.getParams().get("isScope").toString())),
//                busiWinningBidderNotice.getParams().get("dataScope") + ""
//        );

        BaseUtil.checkUser(busiWinningBidderNoticeQueryWrapper, busiWinningBidderNotice);
        return busiWinningBidderNoticeQueryWrapper;
    }

    @Transactional
    @Override
    public AjaxResult saveBusiWinningBidderNotice(BusiWinningBidderNotice busiWinningBidderNotice) {
        try {
            BusiTenderProject project = iBusiTenderProjectService.getById(busiWinningBidderNotice.getProjectId());

            project.setTenderModeName(dictDataService.selectDictLabel("busi_tender_mode", project.getTenderMode()));
            project.setProjectTypeName(dictDataService.selectDictLabel("busi_project_type", project.getProjectType()));
            if(org.apache.commons.lang3.StringUtils.isNoneBlank(project.getTenderFundSource())) {
                project.setTenderFundSourceName(dictDataService.selectDictLabel("tender_project_fundsource", project.getTenderFundSource()));
            }
            if (org.apache.commons.lang3.StringUtils.isNoneBlank(project.getProjectIndustry())) {
                String[] industrys = project.getProjectIndustry().split(",");
                String lastIndustry = industrys[industrys.length - 1];
                BaseTreeData tree = baseTreeDataService.getById(Long.valueOf(lastIndustry));
                project.setProjectIndustryName(tree.getName());
            }
            List<BusiBidderInfo> list = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>()
                    .eq("project_id", busiWinningBidderNotice.getProjectId()).eq("bidder_id", busiWinningBidderNotice.getBidderId())
            );
            BusiBiddingRecord one = iBusiBiddingRecordService.getOne(new QueryWrapper<BusiBiddingRecord>()
                    .eq("project_id", busiWinningBidderNotice.getProjectId())
                    .eq("bidder_id", busiWinningBidderNotice.getBidderId()));


            if (project.getTenderMode().equals("0")||project.getTenderMode().equals("3")||project.getTenderMode().equals("4")){
                list.get(0).setBidderAmount(one.getBidAmount());
            }
            if (list.size()==1){
                list.get(0).setIsWin(1);
                busiBidderInfoService.saveOrUpdate(list.get(0));
            }else {
                return AjaxResult.error("投标人信息错误，请联系管理员！");
            }
            save(busiWinningBidderNotice);
            List<BusiAttachment> attachments = busiWinningBidderNotice.getAttachments();
            if (ObjectUtil.isNotEmpty(attachments)) {
                attachments.forEach(item -> {
                    item.setBusiId(busiWinningBidderNotice.getNoticeId());
                });
                iBusiAttachmentService.saveOrUpdateBatch(attachments);
            }


            String content = createNoticeContent(busiWinningBidderNotice, project,  list.get(0));
            busiWinningBidderNotice.setNoticeContent(content);
            updateById(busiWinningBidderNotice);
//        if(project.getProjectStatus() == 30){
            project.setProjectStatus(60);
            iBusiTenderProjectService.updateById(project);
//        }else{
//            throw new ServiceException("项目状态错误，发布中标结果公告失败");
//        }
            busiWinningBidderAdviceNoteService.saveAdviceNote(busiWinningBidderNotice);
            processInfoService.initInfo(project.getProjectId(), project.getProjectName());

            send2Zcxh(busiWinningBidderNotice, project, "结果公告");
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult pushNotice(BusiWinningBidderNotice busiWinningBidderNotice) {
        //TODO 推送成交通知书  合同签署  归档
        BusiTenderProject project = iBusiTenderProjectService.getById(busiWinningBidderNotice.getProjectId());
        if(project.getProjectStatus() == 50){
            project.setProjectStatus(60);
        }else{
            throw new ServiceException("项目状态错误，发布中标结果公告失败");
        }
        return AjaxResult.success();
    }

    @Transactional
    @Override
    public AjaxResult saveAbortiveTenderNotice(BusiWinningBidderNotice busiWinningBidderNotice) {
        Map<String, Object> params = new HashMap<>();

        if(busiWinningBidderNotice.getAbortiveType()==AbortiveTypeEnum.BINDDING.getCode()){

        }else if(busiWinningBidderNotice.getAbortiveType()==AbortiveTypeEnum.SIGNIN.getCode()){

        }else if(busiWinningBidderNotice.getAbortiveType()==AbortiveTypeEnum.EVALUATION.getCode()){
            //添加流标情况说明 （在评标进度表）
            List<EvalProjectEvaluationInfo> projects = projectEvaluationInfoService.list(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", busiWinningBidderNotice.getProjectId()));
            if(ObjectUtil.isNotEmpty(projects)){
                EvalProjectEvaluationInfo evalProjectEvaluationInfo = projects.get(projects.size() - 1);
                List<EvalProjectEvaluationProcess> list = projectEvaluationProcessService.list(new QueryWrapper<EvalProjectEvaluationProcess>().eq("project_evaluation_id", evalProjectEvaluationInfo.getProjectEvaluationId())
                        .eq("scoring_method_item_id", busiWinningBidderNotice.getScoringMethodItemId()));
                if(ObjectUtil.isNotEmpty(list)){
                    EvalProjectEvaluationProcess evalProjectEvaluationProcess = list.get(list.size() - 1);
                    evalProjectEvaluationProcess.setEvaluationResultRemark(busiWinningBidderNotice.getRemark());
                    evalProjectEvaluationProcess.setEvaluationState(2);
                    projectEvaluationProcessService.updateById(evalProjectEvaluationProcess);
                }
            }
        }else if(busiWinningBidderNotice.getAbortiveType()==AbortiveTypeEnum.DECODE.getCode()){
            BidOpeningOperationRecord bidOpeningOperationRecord = new BidOpeningOperationRecord();
            bidOpeningOperationRecord.setProjectId(busiWinningBidderNotice.getProjectId());
            bidOpeningOperationRecord.setOperationType(6L);
            bidOpeningOperationRecord.setOperationTime(Calendar.getInstance().getTime());
            bidOpeningOperationRecordService.saveRecord(bidOpeningOperationRecord);
        }

        BusiTenderProject project = iBusiTenderProjectService.getSubById(busiWinningBidderNotice.getProjectId());
        if (project == null) {
            throw new RuntimeException("所属项目为空");
        }
        project.setProjectStatus(-2);
        project.systemInsertOrUpdate();
        iBusiTenderProjectService.updateById(project);
        BusiWinningBidderNotice winningBidderNotice = new BusiWinningBidderNotice();
        winningBidderNotice.setProjectId(busiWinningBidderNotice.getProjectId());
        winningBidderNotice.setNoticeName(project.getProjectName()+"—废标公告");
        winningBidderNotice.setRemark(
                StringUtils.isNotEmpty(busiWinningBidderNotice.getRemark())?
                        busiWinningBidderNotice.getRemark():AbortiveTypeEnum.getRemarkByCode(busiWinningBidderNotice.getAbortiveType()).getRemark());
        winningBidderNotice.setNoticeType(3);
        winningBidderNotice.setNoticeStartTime(new Date());
        try {
            String content = createAbortiveTenderNoticeContent(project, winningBidderNotice.getRemark());
            winningBidderNotice.setNoticeContent(content);
            winningBidderNotice.systemInsertOrUpdate();
            save(winningBidderNotice);
            send2Zcxh(winningBidderNotice, project, "终止（废标）公告");
        } catch (Exception e) {
            e.printStackTrace();
            throw  new RuntimeException("流标公告保存失败！");
        }
        return AjaxResult.success();
    }

    //推送至政采协会
    private void send2Zcxh(BusiWinningBidderNotice winningBidderNotice, BusiTenderProject project, String noticeType){
        JSONObject sendData = new JSONObject();
        sendData.put("noticeContent", winningBidderNotice.getNoticeContent());
        sendData.put("noticeTitle", winningBidderNotice.getNoticeName());
        sendData.put("purchasingUnit", project.getTendererName());
        sendData.put("noticeSource", "限额以下");
        sendData.put("noticeType", noticeType);
        sendData.put("publishingTime", winningBidderNotice.getNoticeStartTime().getTime());
        sendData.put("thirdId", winningBidderNotice.getNoticeId());
        sendData.put("tenderType", project.getProjectTypeName());
//        sendData.put("projectArea",project.getProjectAreaName());
//        sendData.put("projectAreaCode",project.getProjectAreaCode());
        if(null!=project.getProjectArea()){
            BaseTreeData area = baseTreeDataService.getById(project.getProjectArea());
            project.setProjectAreaName(area.getName());
            project.setProjectAreaCode(area.getCode());
            sendData.put("projectArea",area.getName());
            sendData.put("projectAreaCode",area.getCode());
        }
        sendData.put("projectType", project.getProjectTypeName());
        sendData.put("projectIndustry", project.getProjectIndustryName());
        sendData.put("noticeUrl", "https://xeyx.hbszfcgxh.com:10443");
        try {
            zcxhUtil.send2Zcxh(sendData, ZcxhEnum.NOTICE.getUrl());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public String createNoticeContent(BusiWinningBidderNotice busiWinningBidderNotice, BusiTenderProject tenderProject, BusiBidderInfo winner) throws IOException, TemplateException {
        String res = "";
        BusiTenderNotice tenderNotice = busiTenderNoticeService.selectByProject(tenderProject.getProjectId());
        BusiExtractExpertResult expertQuery = new BusiExtractExpertResult();
        expertQuery.setProjectId(tenderProject.getProjectId());
        List<BusiExtractExpertResult> expertResults = busiExtractExpertResultService.getZhuanJiaByProjectId(expertQuery);
        String expertList = "";
        for (BusiExtractExpertResult result : expertResults) {
            expertList += result.getExpertName()+"   ";
        }
        BusiVenueOccupy evaluationVenue = busiVenueOccupyService.getOne(
                new QueryWrapper<BusiVenueOccupy>().eq("venue_type", 2).eq("del_flag", 0).eq("notice_id", tenderNotice.getNoticeId()));
        if (winner != null) {
            Map<String, Object> map = new HashMap<>();
            map.put("tenderProject", tenderProject);
            map.put("tenderNotice", tenderNotice);
            map.put("winner", winner);
            map.put("evaluationVenue", evaluationVenue);
            map.put("expertList", expertList);
            map.put("noticeStartTime", DateUtils.getChineseDate(new Date()));
            map.put("fileItems", attachmentUtil.getAttachmentList(busiWinningBidderNotice.getNoticeId(), ProcessEnum.WINNING_BIDDER_NOTICE.getAttachmentCode()));


            String ftlPath = "portal_templates/结果公告.ftl";
            Template template = configurer.getConfiguration().getTemplate(ftlPath);
            res = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
        }
        return res;
    }

    public String createAbortiveTenderNoticeContent(Long projectId, String remark) throws IOException, TemplateException {
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        return createAbortiveTenderNoticeContent(tenderProject, remark);
    }

    public String createAbortiveTenderNoticeContent(BusiTenderProject tenderProject, String remark) throws IOException, TemplateException {
        String res = "";
        BusiBidderInfo query = new BusiBidderInfo();
        query.setProjectId(tenderProject.getProjectId());
        List<BusiBidderInfo> bidderInfo = busiBidderInfoService.selectList(query);
        List<BusiExtractExpertResult> expertResults = busiExtractExpertResultService.getByProject(tenderProject.getProjectId());
        StringBuilder expertList = new StringBuilder();
        for (BusiExtractExpertResult result : expertResults) {
            expertList.append(result.getExpertName()).append("   ");
        }
        BusiTenderNotice tenderNotice = busiTenderNoticeService.selectByProject(tenderProject.getProjectId());
        BusiVenueOccupy evaluationVenue = busiVenueOccupyService.getOne(
                new QueryWrapper<BusiVenueOccupy>().eq("venue_type", 2).eq("del_flag", 0).eq("notice_id", tenderNotice.getNoticeId()));
        Map<String, Object> map = new HashMap<>();
        map.put("tenderProject", tenderProject);
        map.put("tenderNotice", tenderNotice);
        map.put("expertList", expertList.toString());
        String evaluationVenuePlace = "";
        String evaluationTime = "";
        if (evaluationVenue != null) {
            evaluationTime = DateUtils.getChineseDate(evaluationVenue.getOccupyStartTime());
            evaluationVenuePlace = (evaluationVenue.getRemark()!=null?evaluationVenue.getRemark():"") + " " + (evaluationVenue.getVenueName()!=null?evaluationVenue.getVenueName():"");
        }
        map.put("evaluationVenuePlace", evaluationVenuePlace);
        map.put("evaluationTime", evaluationTime);
        map.put("remark", remark);
        map.put("noticeStartTime", DateUtils.getChineseDate(new Date()));

        String ftlPath = "portal_templates/流标公告.ftl";
        Template template = configurer.getConfiguration().getTemplate(ftlPath);
        res = FreeMarkerTemplateUtils.processTemplateIntoString(template, map);
        return res;
    }

    @Override
    public BusiTenderVo view(Long noticeId) {
        BusiTenderVo vo = new BusiTenderVo();
        BusiWinningBidderNotice winningBidderNotice = baseMapper.selectById(noticeId);
        if (winningBidderNotice != null) {
            BusiTenderProject tenderProject = iBusiTenderProjectService.getById(winningBidderNotice.getProjectId());
            winningBidderNotice.setAttachmentMap(attachmentUtil.getAttachmentMap(winningBidderNotice.getNoticeId(), ProcessEnum.WINNING_BIDDER_NOTICE.getAttachmentCode()));

            List<BusiAttachment> busiAttachments = iBusiAttachmentService.selectByBusiId(winningBidderNotice.getNoticeId());

            winningBidderNotice.setAttachments(busiAttachments);
            vo.setWinningBidderNotice(winningBidderNotice);
            vo.setTenderProject(tenderProject);
        }


        return vo;
    }

    public static void main(String[] args) {
        BusiVenueOccupy evaluationVenue = new BusiVenueOccupy();
        String s = (evaluationVenue.getRemark()!=null?evaluationVenue.getRemark():"") + " :  " + (evaluationVenue.getVenueName()!=null?evaluationVenue.getVenueName():"");
        System.out.println(evaluationVenue.getRemark()+" :  "+evaluationVenue.getVenueName());
        System.out.println(s);
        System.out.println(evaluationVenue);
        System.out.println(evaluationVenue.getRemark());
    }
}
