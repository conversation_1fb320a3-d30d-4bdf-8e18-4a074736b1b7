{"remainingRequest": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\three.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\src\\views\\expertReview\\qualification\\three.vue", "mtime": 1754129323470}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\@vue\\cli-plugin-babel\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1750996952728}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750996948462}, {"path": "D:\\yunzhonghe\\xeyxjypt\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1750996949840}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["three.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAq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file": "three.vue", "sourceRoot": "src/views/expertReview/qualification", "sourcesContent": ["<template>\r\n  <div class=\"three\">\r\n    <div style=\"width:70%\">\r\n      <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px\">资格性评审</div>\r\n\r\n      <el-table :data=\"tableData\" border style=\"width: 100%\" :header-cell-style=\"headStyle\" :cell-style=\"cellStyle\">\r\n        <el-table-column prop=\"供应商名称\" width=\"180\">\r\n        </el-table-column>\r\n        <el-table-column v-for=\"(item, index) in columns\" :key=\"index\" :prop=\"item.xm\" :label=\"item.xm\">\r\n          <template slot-scope=\"scope\">\r\n\t          <span v-if=\"scope.row[item.xm] == '/'\">未提交</span>\r\n            <span v-if=\"scope.row[item.xm] == '1'\">通过</span>\r\n            <span v-if=\"scope.row[item.xm] == '0'\">不通过</span>\r\n            <!-- <i v-else style=\"color:#176ADB;font-size:20px\" :class=\"getIconClass(scope.row[item.xm])\"></i> -->\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\t    \r\n      <div class=\"result\">\r\n        <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;margin-bottom:20px\">评审结果：</div>\r\n        <div style=\"display: flex;margin-left:30px\">\r\n          <div style=\"margin-right:30px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;\" v-for=\"(item,index) in (Array.isArray(result) ? result : [])\" :key=\"index\">\r\n            {{ item.gys }}：\r\n            <span v-if=\"item.result\" style=\"color:green\">\r\n              通过\r\n            </span>\r\n            <span v-else style=\"color:red\">\r\n              不通过\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"operation\" v-if=\"!finish\">\r\n        <el-button\r\n          class=\"item-button\"\r\n          style=\"background-color: #f5f5f5;color: #176adb;\"\r\n          @click=\"reviewed\"\r\n        >重新评审</el-button>\r\n        <el-button class=\"item-button\" v-if=\"passedSupplierCount >= 3\" @click=\"completed\">节点评审完成</el-button>\r\n        <el-button class=\"item-button-red\"\r\n                   v-if=\"!hasIncompleteExpert\"\r\n                   :disabled=\"passedSupplierCount >= 3\"\r\n                   :style=\"passedSupplierCount >= 3 ? 'background-color: #ccc; color: #fff; cursor: not-allowed;' : ''\"\r\n                   @click=\"flowLabel\">流标</el-button>\r\n      </div>\r\n      <div v-else class=\"operation\">\r\n        <el-button class=\"item-button\" @click=\"back\">返回</el-button>\r\n      </div>\r\n    </div>\r\n    <div style=\"width:30%\">\r\n      <div class=\"result\">\r\n        <div style=\"font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px\">表决结果</div>\r\n        <el-input disabled class=\"text\" type=\"textarea\" :rows=\"20\" placeholder=\"请输入表决结果\" v-model=\"votingResults\">\r\n        </el-input>\r\n      </div>\r\n    </div>\r\n    <el-dialog title=\"流标情况说明\" :visible.sync=\"dialogVisible\" width=\"30%\">\r\n      <el-input type=\"textarea\" :rows=\"4\" placeholder=\"请输入内容\" v-model=\"reasonFlowBid\">\r\n      </el-input>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"confirmflow\">确 定</el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\n// 导入专家评审相关API接口\r\nimport {\r\n  leaderSummaryQuery,  // 组长汇总查询接口\r\n  reEvaluate,         // 重新评审接口\r\n  expertInfoById,     // 根据ID获取专家信息接口\r\n} from \"@/api/expert/review\";\r\n\r\n// 导入评审流程相关API接口\r\nimport { updateProcess } from \"@/api/evaluation/process\";        // 更新评审流程接口\r\nimport { abortiveTenderNotice } from \"@/api/bidder/notice\";      // 流标通知接口\r\nimport { reEvaluationTwo } from \"@/api/evaluation/expertStatus\"; // 重新评审状态接口\r\n\r\nexport default {\r\n  // 组件属性定义\r\n  props: {\r\n    // 是否完成评审的标识\r\n    finish: {\r\n      type: Boolean,    // 布尔类型\r\n      default: false,   // 默认值为false，表示未完成\r\n    },\r\n  },\r\n  // 组件数据定义\r\n  data() {\r\n    return {\r\n      // 表格数据数组，存储评审表格的行数据\r\n      tableData: [],\r\n\r\n      // 表格列配置对象，存储表格列的定义信息\r\n      columns: {},\r\n\r\n      // 评审结果数组，存储每个供应商的评审结果\r\n      result: [], // 修改：初始化为数组而不是对象\r\n\r\n      // 表决结果文本，存储专家组的表决意见\r\n      votingResults: \"\",\r\n\r\n      // 流标原因，当需要流标时填写的原因\r\n      reasonFlowBid: \"\",\r\n\r\n      // 对话框显示状态控制\r\n      dialogVisible: false,\r\n\r\n      // 未评审专家列表，存储尚未完成评审的专家信息\r\n      unreviewedExperts: [],\r\n\r\n      // 组长信息对象\r\n      leader: {},\r\n\r\n      // 定时器ID，用于清除定时器\r\n      intervalId: null,\r\n\r\n      // 表格头部样式配置\r\n      headStyle: {\r\n        \"text-align\": \"center\",                    // 文字居中对齐\r\n        \"font-family\": \"SourceHanSansSC-Bold\",     // 字体家族\r\n        background: \"#176ADB\",                     // 背景色（蓝色）\r\n        color: \"#fff\",                             // 文字颜色（白色）\r\n        \"font-size\": \"16px\",                       // 字体大小\r\n        \"font-weight\": \"700\",                      // 字体粗细（加粗）\r\n        border: \"0\",                               // 边框设置\r\n      },\r\n\r\n      // 表格单元格样式配置\r\n      cellStyle: {\r\n        \"text-align\": \"center\",                    // 文字居中对齐\r\n        \"font-family\": \"SourceHanSansSC-Bold\",     // 字体家族\r\n        height: \"60px\",                            // 单元格高度\r\n        color: \"#000\",                             // 文字颜色（黑色）\r\n        \"font-size\": \"14px\",                       // 字体大小\r\n        \"font-weight\": \"700\",                      // 字体粗细（加粗）\r\n      },\r\n    };\r\n  },\r\n  // 组件方法定义\r\n  methods: {\r\n    /**\r\n     * 初始化方法\r\n     * 获取评审数据并处理显示\r\n     */\r\n    init() {\r\n      // 构建请求参数\r\n      const data = {\r\n        projectId: this.$route.query.projectId,           // 项目ID（从路由参数获取）\r\n        itemId: this.$route.query.scoringMethodItemId,    // 评分方法项目ID（从路由参数获取）\r\n      };\r\n\r\n      // 调用组长汇总查询接口\r\n      leaderSummaryQuery(data).then((response) => {\r\n        if (response.code == 200) {\r\n          // 请求成功，处理返回数据\r\n\t        \r\n          // 设置表决结果文本\r\n          this.votingResults = response.data.bjjgsb;\r\n\r\n          // 设置未评审专家列表\r\n          this.unreviewedExperts = response.data.wpszj;\r\n\r\n          // 转换并设置表格数据\r\n          this.tableData = this.transformData(\r\n            response.data.tableColumns,    // 表格列配置\r\n            response.data.busiBidderInfos, // 投标人信息\r\n            response.data.tableData        // 原始表格数据\r\n          );\r\n\r\n          // 过滤掉已废标的数据（isAbandonedBid == 0 表示未废标）\r\n          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)\r\n\r\n          // 设置表格列配置\r\n          this.columns = response.data.tableColumns;\r\n\r\n          // 生成评审结果表\r\n          this.result = this.generateResultTable(\r\n            response.data.tableColumns,    // 表格列配置\r\n            response.data.busiBidderInfos, // 投标人信息\r\n            response.data.tableData        // 原始表格数据\r\n          );\r\n\r\n          console.log(this.result)\r\n\r\n          // 添加安全检查：确保 result 是数组后再进行过滤\r\n          if (Array.isArray(this.result)) {\r\n            // 过滤掉已废标的评审结果\r\n            this.result = this.result.filter(item => item.isAbandonedBid == 0)\r\n          } else {\r\n            console.error(\"generateResultTable did not return an array:\", this.result);\r\n            this.result = []; // 设置为空数组作为后备\r\n          }\r\n\r\n          console.log(this.tableData)\r\n\t        \r\n        } else {\r\n          // 请求失败，显示警告信息\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n    /**\r\n     * 数据转换函数\r\n     * 将原始数据转换为表格显示所需的格式\r\n     * @param {Array} tableColumns - 表格列配置数组\r\n     * @param {Array} busiBidderInfos - 投标人信息数组\r\n     * @param {Array} tableData - 原始表格数据数组\r\n     * @returns {Array} 转换后的表格数据\r\n     */\r\n    transformData(tableColumns, busiBidderInfos, tableData) {\r\n      // 创建投标人ID到投标人信息的映射\r\n      // 用于快速查找投标人名称和废标状态\r\n      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {\r\n        acc[info.bidderId] = {\r\n          bidderName: info.bidderName,                    // 投标人名称\r\n          isAbandonedBid: info.isAbandonedBid || 0        // 是否废标（默认为0，表示未废标）\r\n        };\r\n        return acc;\r\n      }, {});\r\n\t\t\t\r\n      // 创建结果ID到项目名称的映射（虽然当前未使用，但保留以备后用）\r\n      const columnIdToName = tableColumns.reduce((acc, column) => {\r\n        acc[column.resultId] = column.xm;\r\n        return acc;\r\n      }, {});\r\n\r\n      // 转换原始数据为表格显示格式\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;  // 供应商ID\r\n        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];\r\n\r\n        // 初始化转换后的行数据，包含供应商名称和废标状态\r\n        const transformedRow = {\r\n          供应商名称: bidderName,\r\n          isAbandonedBid: isAbandonedBid\r\n        };\r\n\r\n        // 遍历表格列配置，将对应的评估结果添加到行数据中\r\n        tableColumns.forEach((column) => {\r\n          const itemId = column.resultId;  // 评估项ID\r\n          // 设置评估结果，如果没有找到对应值则默认为'/'(没有评完)\r\n          transformedRow[column.xm] = row[itemId] || \"/\";\r\n        });\r\n\r\n        return transformedRow;\r\n      });\r\n    },\r\n    /**\r\n     * 组装评审结果方法\r\n     * 根据评审数据生成最终的评审结果，采用少数服从多数的原则\r\n     * @param {Array} tableColumns - 表格列配置数组\r\n     * @param {Array} busiBidderInfos - 投标人信息数组\r\n     * @param {Array} tableData - 原始表格数据数组\r\n     * @returns {Array} 评审结果数组，每个元素包含投标人信息和评审结果\r\n     */\r\n    generateResultTable(tableColumns, busiBidderInfos, tableData) {\r\n      // 添加安全检查：确保输入参数都是数组\r\n      if (!Array.isArray(tableColumns) || !Array.isArray(busiBidderInfos) || !Array.isArray(tableData)) {\r\n        console.error(\"generateResultTable: Invalid input parameters\", {\r\n          tableColumns: tableColumns,\r\n          busiBidderInfos: busiBidderInfos,\r\n          tableData: tableData\r\n        });\r\n        return []; // 返回空数组作为后备\r\n      }\r\n\r\n      // 提取所有评估项的ID\r\n      const entMethodItemIds = tableColumns.map((item) => {\r\n        return item.resultId;\r\n      });\r\n\r\n      // 创建投标人ID到投标人信息的映射\r\n      // 使用Map数据结构提高查找效率\r\n      const bidderMap = new Map(\r\n        busiBidderInfos.map((bidder) => [bidder.bidderId, {\r\n          bidderName: bidder.bidderName,                    // 投标人名称\r\n          isAbandonedBid: bidder.isAbandonedBid || 0        // 是否废标状态\r\n        }])\r\n      );\r\n\r\n      // 生成结果表，按照少数服从多数规则判断是否通过\r\n      return tableData.map((row) => {\r\n        const supplierId = row.gys;  // 获取供应商ID\r\n\r\n        // 从映射中获取投标人信息\r\n        const { bidderName, isAbandonedBid } = bidderMap.get(supplierId);\r\n\r\n        // 计算评审结果\r\n        const totalItems = entMethodItemIds.length;  // 总评估项数量\r\n        let passedCount = 0;  // 通过的评估项数量\r\n\r\n        // 遍历所有评估项，统计通过数量\r\n        entMethodItemIds.forEach((key) => {\r\n          if (row[key] == \"1\") {  // \"1\" 表示该评估项通过\r\n            passedCount++;\r\n          }\r\n        });\r\n\r\n        // 少数服从多数原则：通过数量 >= 总数的一半（向上取整）则判定为通过\r\n        const result = passedCount >= Math.ceil(totalItems / 2);\r\n\r\n        // 返回评审结果对象\r\n        return {\r\n          bidder: supplierId,           // 投标人ID\r\n          gys: bidderName,              // 投标人名称\r\n          isAbandonedBid: isAbandonedBid, // 是否废标\r\n          result: result,               // 评审结果（true: 通过, false: 不通过）\r\n        };\r\n      });\r\n    },\r\n    /**\r\n     * 节点评审完成方法\r\n     * 处理评审完成的逻辑，包括验证和提交评审结果\r\n     */\r\n    completed() {\r\n      // 检查是否有未完成评审的专家\r\n      if (this.unreviewedExperts && this.unreviewedExperts.length > 0){\r\n        // 收集未评审专家的姓名\r\n        let result = [];\r\n        for(let i = 0; i < this.unreviewedExperts.length; i++){\r\n          result.push(this.unreviewedExperts[i].xm)\r\n        }\r\n        // 显示错误提示，列出未完成评审的专家\r\n        this.$message.error(`专家${result.join(\"、\")}未完成评审！`);\r\n        return\r\n      }\r\n\r\n      // 二次确认提示\r\n      this.$confirm('是否确认完成节点评审？', '确认完成评审', {\r\n        confirmButtonText: '是',\r\n        cancelButtonText: '否',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        // 所有专家都已完成评审，继续处理评审完成逻辑\r\n\r\n        // 从本地存储获取评审流程ID\r\n        const evaluationProcessId = JSON.parse(\r\n          localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n        );\r\n\r\n        // 构建更新评审流程的请求数据\r\n        const data = {\r\n          evaluationProcessId: evaluationProcessId.evaluationProcessId,  // 评审流程ID\r\n          evaluationResult: JSON.stringify(this.result),                 // 评审结果（JSON字符串）\r\n          evaluationState: 2,                                            // 评审状态（2表示已完成）\r\n          evaluationResultRemark: this.votingResults,                    // 评审结果备注\r\n        };\r\n\r\n        // 调用更新评审流程接口\r\n        updateProcess(data).then((response) => {\r\n          if (response.code == 200) {\r\n            // 更新成功，跳转到专家信息页面\r\n            this.$router.push({\r\n              path: \"/expertInfo\",\r\n              query: {\r\n                projectId: this.$route.query.projectId,  // 项目ID\r\n                zjhm: this.$route.query.zjhm,            // 专家号码\r\n              },\r\n            });\r\n          } else {\r\n            // 更新失败，显示警告信息\r\n            this.$message.warning(response.msg);\r\n          }\r\n        });\r\n      }).catch(() => {\r\n        this.$message.info('已取消完成评审');\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 返回方法\r\n     * 返回到专家信息页面\r\n     */\r\n    back() {\r\n      this.$router.push({\r\n        path: \"/expertInfo\",\r\n        query: {\r\n          projectId: this.$route.query.projectId,  // 项目ID\r\n          zjhm: this.$route.query.zjhm,            // 专家号码\r\n        },\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 获取图标样式类名\r\n     * 根据评审结果值返回对应的图标类名\r\n     * @param {string} value - 评审结果值\r\n     * @returns {string} 图标类名\r\n     */\r\n    getIconClass(value) {\r\n      if (value == \"1\"){\r\n        return \"el-icon-check\"           // 通过：显示勾选图标\r\n      }\r\n\r\n      if (value == \"0\"){\r\n        return \"el-icon-circle-close\"    // 不通过：显示关闭图标\r\n      }\r\n\r\n      return value  // 其他情况直接返回原值\r\n    },\r\n\r\n    /**\r\n     * 重新评审方法\r\n     * 触发重新评审流程，重置评审状态\r\n     */\r\n    reviewed() {\r\n      // 从本地存储获取专家评分信息，构建查询参数\r\n      const query = {\r\n        projectEvaluationId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).projectEvaluationId,                                          // 项目评审ID\r\n        expertResultId: JSON.parse(localStorage.getItem(\"evalExpertScoreInfo\"))\r\n          .expertResultId,                                              // 专家结果ID\r\n        scoringMethodItemId: JSON.parse(\r\n          localStorage.getItem(\"evalExpertScoreInfo\")\r\n        ).scoringMethodItemId,                                          // 评分方法项目ID\r\n      };\r\n\r\n      // 调用重新评审接口\r\n      reEvaluationTwo(query).then((res) => {\r\n        if (res.code == 200) {\r\n          // 第一步成功，获取评审流程ID\r\n          const evaluationProcessId = JSON.parse(\r\n            localStorage.getItem(\"evalProjectEvaluationProcess\")\r\n          );\r\n\r\n          // 调用重新评审接口\r\n          reEvaluate(evaluationProcessId.evaluationProcessId).then(\r\n            (response) => {\r\n              if (response.code == 200) {\r\n                // 触发重新评审通知，通知其他专家页面\r\n                if (this.$parent && typeof this.$parent.triggerReEvaluationNotification === 'function') {\r\n                  debugger\r\n\t\t\t\t\t\t\t\t\tthis.$parent.triggerReEvaluationNotification();\r\n                }\r\n                // 重新评审成功，发送事件通知父组件切换到第一步\r\n                this.$emit(\"send\", \"one\");\r\n              } else {\r\n                // 重新评审失败，显示警告信息\r\n                this.$message.warning(response.msg);\r\n              }\r\n            }\r\n          );\r\n        }\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 流标方法\r\n     * 显示流标确认对话框\r\n     */\r\n    flowLabel() {\r\n      this.dialogVisible = true;\r\n    },\r\n    /**\r\n     * 确认流标方法\r\n     * 处理流标确认逻辑，发送流标通知\r\n     */\r\n    confirmflow() {\r\n      // 验证流标原因是否填写\r\n      if (this.reasonFlowBid == \"\") {\r\n        this.$message.warning(\"请完善情况说明\")\r\n        return;\r\n      }\r\n\r\n      // 构建流标通知请求数据\r\n      const data = {\r\n        projectId: this.$route.query.projectId,              // 项目ID\r\n        abortiveType: 3,                                     // 流标类型（3表示资格性评审流标）\r\n        remark: this.reasonFlowBid,                          // 流标原因说明\r\n        scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项目ID\r\n      };\r\n\r\n      // 调用流标通知接口\r\n      abortiveTenderNotice(data).then((response) => {\r\n        if (response.code == 200) {\r\n          // 流标通知发送成功，跳转到汇总页面\r\n          const query = {\r\n            projectId: this.$route.query.projectId,          // 项目ID\r\n            zjhm: this.$route.query.zjhm,                    // 专家号码\r\n            scoringMethodItemId: this.$route.query.scoringMethodItemId, // 评分方法项目ID\r\n          };\r\n          this.$router.push({ path: \"/summary\", query: query });\r\n        } else {\r\n          // 流标通知发送失败，显示警告信息\r\n          this.$message.warning(response.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 清除定时器的通用方法\r\n     * 在多个生命周期钩子中调用，确保定时器被正确清除\r\n     */\r\n    clearTimer() {\r\n      if (this.intervalId) {\r\n        clearInterval(this.intervalId);\r\n        this.intervalId = null;\r\n        console.log(\"定时器已清除 - qualification/three.vue\");\r\n      }\r\n    },\r\n  },\r\n\r\n  // 计算属性\r\n  computed:{\r\n    /**\r\n     * 通过评审的供应商数量\r\n     * 统计评审结果中通过的供应商数量\r\n     * @returns {number} 通过评审的供应商数量\r\n     */\r\n    passedSupplierCount() {\r\n      console.log(\"this.result:\",this.result);\r\n      // 添加安全检查：确保 result 是数组\r\n      if (!Array.isArray(this.result)) {\r\n        console.warn(\"result is not an array:\", this.result);\r\n        return 0;\r\n      }\r\n      // 过滤出评审结果为通过的供应商，返回数量\r\n      return this.result.filter(item => item.result).length;\r\n    },\r\n\r\n    /**\r\n     * 检查是否有专家未完成评审\r\n     * 遍历tableData检查是否存在\"/\"状态（未评完）\r\n     * @returns {boolean} true表示有未完成评审的专家，false表示所有专家都已完成评审\r\n     */\r\n    hasIncompleteExpert() {\r\n      // 添加安全检查：确保 tableData 是数组\r\n      if (!Array.isArray(this.tableData)) {\r\n        console.warn(\"tableData is not an array:\", this.tableData);\r\n        return true; // 数据异常时，默认禁用流标按钮\r\n      }\r\n\r\n      // 遍历所有供应商的评审数据\r\n      const hasIncomplete = this.tableData.some(row => {\r\n        // 遍历每一行的所有属性，查找是否有\"/\"状态\r\n        return Object.keys(row).some(key => {\r\n          // 排除供应商名称和废标状态字段，只检查专家评审结果\r\n          if (key !== '供应商名称' && key !== 'isAbandonedBid') {\r\n            return row[key] === '/';\r\n          }\r\n          return false;\r\n        });\r\n      });\r\n\r\n      // 输出调试信息\r\n      console.log(\"hasIncompleteExpert:\", hasIncomplete, \"tableData:\", this.tableData);\r\n      return hasIncomplete;\r\n    }\r\n  },\r\n\r\n  // 生命周期钩子\r\n  /**\r\n   * 组件挂载完成后执行\r\n   * 初始化组件数据和定时刷新\r\n   */\r\n  mounted() {\r\n    // 初始化数据\r\n    this.init();\r\n\r\n    // 设置定时器，每5秒自动刷新数据\r\n    // 用于实时更新评审状态和结果\r\n    this.intervalId = setInterval(()=>{\r\n      this.init();\r\n    },5000)\r\n  },\r\n\r\n  /**\r\n   * 组件销毁前执行\r\n   * 清除定时器，防止内存泄漏\r\n   */\r\n  beforeDestroy() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 组件完全销毁后执行\r\n   * 作为额外的安全措施清除定时器\r\n   */\r\n  destroyed() {\r\n    this.clearTimer();\r\n  },\r\n\r\n  /**\r\n   * 如果父组件使用了keep-alive，在组件失活时清除定时器\r\n   */\r\n  deactivated() {\r\n    this.clearTimer();\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.three {\r\n  padding: 20px 40px;\r\n  display: flex;\r\n}\r\n.el-header {\r\n  background-color: #fff;\r\n  color: #333;\r\n  font-size: 26px;\r\n  text-align: center;\r\n  line-height: 100px;\r\n  border-bottom: #333 1px solid;\r\n}\r\n.el-main {\r\n  background-color: #fff;\r\n  color: #333;\r\n  text-align: center;\r\n  line-height: 60px;\r\n}\r\n.item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  font-size: 18px;\r\n  margin-bottom: 80px;\r\n  .item-title {\r\n    width: 120px;\r\n    margin-right: 20px;\r\n    text-align: left;\r\n  }\r\n}\r\n.item-button {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #fff;\r\n  background-color: #176adb;\r\n  border: 0;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.item-button-red {\r\n  width: 150px;\r\n  height: 40px;\r\n  margin: 20px 28px;\r\n  color: #fff;\r\n  background-color: #e92900;\r\n  border: 0;\r\n  &:hover {\r\n    color: #fff;\r\n  }\r\n}\r\n.result {\r\n  text-align: left;\r\n  margin-left: 20px;\r\n}\r\n.operation {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n.text {\r\n  ::v-deep .el-textarea__inner {\r\n    background-color: #f5f5f5;\r\n    border-radius: 0;\r\n    border: 1px solid #f5f5f5;\r\n  }\r\n}\r\n</style>\r\n"]}]}