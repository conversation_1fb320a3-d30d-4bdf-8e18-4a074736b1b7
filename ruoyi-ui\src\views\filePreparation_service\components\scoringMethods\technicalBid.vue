<template>
  <div class="container">
    <div class="transfer">
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <span>合计： <strong>{{calculateTotalScore()}}</strong>分</span>
        <el-button class="item-button" style="background-color: #fff; height: 40px; color: #333" @click="open()">新增</el-button>
      </div>
      <el-table :data="itemInfos" border style="width: 100%">
        <el-table-column label="序号" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="itemName" label="评审因素" width="180" align="center">
        </el-table-column>
        <el-table-column prop="itemRemark" label="评审内容" align="center">
        </el-table-column>
        <el-table-column label="分值" align="center">
          <template slot-scope="scope">
            <div style="margin-bottom: 5px">
              <span>评分挡位:</span>
              {{ scope.row.scoreLevel }}
              <!-- <el-input
                v-model="scope.row.maxScore"
                style="width: 80px"
              ></el-input> -->
            </div>
            <div>
              <span>分值:</span>
              {{ scope.row.score }}
              <!-- <el-input
                v-model="scope.row.minScore"
                style="width: 80px"
              ></el-input> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-row>
              <el-col :span="24">
                <el-button @click="open(scope.row)" type="text" size="small">编辑</el-button>
              </el-col>
              <el-col :span="24">
                <el-button  @click="handleClick(scope.row)" type="text" size="small">删除</el-button>
              </el-col>
            </el-row>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog :title="title" :visible.sync="dialogVisible" width="40%">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="评审因素" prop="itemName">
          <!-- 当字典数据已加载且有对应选项时显示下拉框 -->
          <el-select v-if="isDictLoaded && currentDictOptions && currentDictOptions.length > 0 && activeParams && activeParams.name"
                     v-model="form.itemName"
                     :disabled="isItemNameReadOnly"
                     placeholder="请选择或输入自定义内容"
                     filterable
                     allow-create
                     default-first-option
                     clearable>
            <el-option v-for="(item, index) in currentDictOptions"
                       :key="index"
                       :label="item.label"
                       :value="item.label">
            </el-option>
          </el-select>
          <!-- 备用输入框，当字典未加载或无对应选项时显示 -->
          <el-input v-else
                    v-model="form.itemName"
                    :disabled="isItemNameReadOnly"
                    placeholder="请输入评审因素">
          </el-input>
        </el-form-item>
        <el-form-item label="评审内容" prop="itemRemark">
          <el-input v-model="form.itemRemark" show-word-limit type="textarea" :rows="8">{{ form.itemRemark }}</el-input>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分数挡位" prop="scoreLevel">
              <el-input v-model="form.scoreLevel"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="满分" prop="score">
              <el-input v-model="form.score"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div style="margin-left: 20px;margin-top: -25px;font-size: 12px; color: #bebfc3;">
              <span>示例：可输入类似"1,2,3"格式；如该评审因素不分档，则无需填写</span>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleUpdate()">保 存</el-button>
        <el-button @click="handleCancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { saveInfo as saveUinfo, updateUinfo } from "@/api/method/uinfo";
import { delUitem, listUitem, updateUitem, addUitem } from "@/api/method/uitem";
export default {
  dicts: ["service_project_jsb_uitem", "service_project_swb_uitem"],
  data() {
    return {
      itemInfos: [],
      dialogVisible: false,
      form: {
        entMethodItemId: "",
        itemName: "",
        itemRemark: "",
        scoreLevel: "",
        score: "",
      },
      rules: {
        itemName: [
          { required: true, message: "请输入评审因素", trigger: "blur" },
        ],
        itemRemark: [
          { required: true, message: "请输入评审内容", trigger: "blur" },
        ],
        scoreLevel: [
          {
            required: true,
            message: "仅数字和英文逗号",
            trigger: "blur",
            pattern: /^[0-9,]+$/,  // 新增正则验证，只能输入数字和英文逗号
          },
        ],
        score: [{ required: false, message: "请输入分值", trigger: "blur" }],
	      
      },
      projectInfo: {},
      uinfo: {},
      tempItem: {},
      activeParams: {},
      title: "新增评审信息",
    };
  },
  computed: {
    isItemNameReadOnly() {
      return this.title === "修改评审信息";
    },
    // 检查字典数据是否已加载
    isDictLoaded() {
      return this.dict && this.dict.type &&
             this.dict.type.service_project_jsb_uitem &&
             this.dict.type.service_project_swb_uitem;
    },
    // 获取当前应该显示的字典选项
    currentDictOptions() {
      if (!this.isDictLoaded) {
        console.log('Dict not loaded yet');
        return [];
      }

      if (!this.activeParams || !this.activeParams.name) {
        console.log('ActiveParams or activeParams.name is empty:', this.activeParams);
        return [];
      }

      let options = [];
      if (this.activeParams.name.includes('技术标')) {
        options = this.dict.type.service_project_jsb_uitem || [];
        console.log('Using 技术标 options:', options.length);
      } else if (this.activeParams.name.includes('商务标')) {
        options = this.dict.type.service_project_swb_uitem || [];
        console.log('Using 商务标 options:', options.length);
      }

      return options;
    },
  },
  watch: {
    // 监听字典数据变化，确保在字典加载完成后能正确显示
    isDictLoaded(newVal) {
      if (newVal && this.dialogVisible) {
        // 字典数据加载完成且对话框已打开，强制更新视图
        this.$nextTick(() => {
          this.$forceUpdate();
        });
      }
    },
    // 监听对话框状态变化
    dialogVisible(newVal) {
      if (newVal) {
        // 对话框打开时，确保状态正确
        this.$nextTick(() => {
          this.ensureCorrectState();
        });
      }
    }
  },
  methods: {
    // 确保状态正确的方法
    ensureCorrectState() {
      console.log('Ensuring correct state...');
      console.log('activeParams:', this.activeParams);
      console.log('isDictLoaded:', this.isDictLoaded);
      console.log('currentDictOptions:', this.currentDictOptions);

      // 如果activeParams为空或者name为空，尝试从父组件重新获取
      if (!this.activeParams || !this.activeParams.name) {
        console.warn('activeParams is invalid, this might cause display issues');
      }

      // 强制更新视图
      this.$forceUpdate();
    },
    // 重新加载数据的方法
    refreshTable() {
      let query = {
        entMethodId: this.uInfo.entMethodId,
        scoringMethodItemId: this.tempItem.scoringMethodItemId,
        entId: this.uInfo.entId,
      };
      listUitem(query).then((result) => {
        this.itemInfos = result.rows;
      });
    },
    //初始化信息
    init(projectInfo, itemInfos, uinfo, tempItem, activeParams) {
      console.log("technicalBid init start", itemInfos);
      this.projectInfo = projectInfo;
      this.activeParams = activeParams;
      console.log(this.activeParams )
      this.itemInfos = itemInfos;
      if (uinfo && uinfo.entMethodId) {
        this.uInfo = uinfo;
      }
      this.tempItem = tempItem;
      console.log("technicalBid init end", this.itemInfos, this.tempItem);
    },
    //删除
    handleClick(row) {
      this.$modal
        .confirm(`是否确认删除评审因素【${row.itemName}】？`)
        .then(() => {
          delUitem(row.entMethodItemId)
            .then((result) => {
              if (result.code === 200) {
                this.$message.success("删除成功");
                this.itemInfos.splice(this.itemInfos.indexOf(row), 1);
                this.$emit("deleteSuccess", this.activeParams);
              }
            })
            .catch((err) => { });
        })
        .catch(() => { });
    },
    // 新建开标项
    open(row) {
      if (row) {
        this.title = "修改评审信息";
        this.form.entMethodItemId = row.entMethodItemId;
        this.form.itemName = row.itemName;
        this.form.itemRemark = row.itemRemark;
        this.form.scoreLevel = row.scoreLevel;
        this.form.score = row.score;
      } else {
        this.title = "新增评审信息";
        this.form.entMethodItemId = "";
        this.form.itemName = "";
        this.form.itemRemark = "";
        this.form.scoreLevel = "";
        this.form.score = "";
      }

      this.dialogVisible = true;

      // 确保在对话框打开后，强制更新一次视图
      this.$nextTick(() => {
        // 添加调试信息
        console.log('Dialog opened - activeParams.name:', this.activeParams.name);
        console.log('Dialog opened - isDictLoaded:', this.isDictLoaded);
        console.log('Dialog opened - currentDictOptions.length:', this.currentDictOptions.length);
        console.log('Dialog opened - dict data:', this.dict);

        this.$forceUpdate();
      });
    },
    // 确认
    handleConfirm() {
      let a = {
        itemName: this.form.itemName,
        itemRemark: this.form.itemRemark,
        scoreLevel: this.form.scoreLevel,
        score: this.form.score,
      };
      if (this.$refs.form) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.itemInfos.push(a);
            this.saveUinfo();
            this.dialogVisible = false;
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      } else {
        console.error("Form ref is undefined");
      }
    },
    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },
    saveUinfo() {
      const postData = {
        entMethodId: this.uInfo.entMethodId,
        scoringMethodId: this.tempItem.scoringMethodId,
        projectId: this.projectInfo.projectId,
        scoringMethodUitems: this.itemInfos.map((item) => {
          return {
            entMethodId: this.uInfo.entMethodId,
            entMethodItemId: item.entMethodItemId,
            scoringMethodId: this.tempItem.scoringMethodId,
            scoringMethodItemId: this.tempItem.scoringMethodItemId,
            itemRemark: item.itemRemark,
            itemName: item.itemName,
            scoreLevel: item.scoreLevel,
            score: item.score,
          };
        }),
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
    handleUpdate() {
      if (this.$refs.form) {
        // 分值验证逻辑优化
        if(this.form.scoreLevel != null && this.form.scoreLevel != ''){
          const parts = this.form.scoreLevel.split(',').map(item => parseFloat(item.trim())).filter(item => !isNaN(item));
          if(parts.length > 0) {
            const maxScoreLevel = Math.max(...parts);
            const formScore = parseFloat(this.form.score);
            if(formScore != maxScoreLevel){
              this.$message.warning(`挡位最大值(${maxScoreLevel})必须等于分数最大值(${formScore})`);
              return;
            }
          }
        }
        
        // 确保分数为有效数值
        if(this.form.score && isNaN(parseFloat(this.form.score))) {
          this.$message.warning("分数必须是有效数值");
          return;
        }
        
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (this.form.entMethodItemId == "") {
              console.log("this.uInfo", this.uInfo);
              let existing_itemNames = this.itemInfos.map(item => item.itemName);
              if (!existing_itemNames.includes(this.form.itemName)) {
                console.log("不包含")
                let putData = {
                  entMethodItemId: this.form.entMethodItemId,
                  itemName: this.form.itemName,
                  itemRemark: this.form.itemRemark,
                  scoreLevel: this.form.scoreLevel,
                  score: this.form.score,
                  entMethodId: this.uInfo.entMethodId,
                  scoringMethodId: this.tempItem.scoringMethodId,
                  scoringMethodItemId: this.tempItem.scoringMethodItemId,
                  entId: this.uInfo.entId,
                };
                addUitem(putData).then((response) => {
                  if (response.code === 200) {
                    this.dialogVisible = false;
                    this.$message.success("新增成功");
                    this.refreshTable();
                    this.$emit("saveSuccess", response.data);
                  }
                });
              }else {
                console.log("包含")
                // 如果已存在，弹出提示框告知用户不能重复选择
                this.$alert("选项不能重复选择，请重新输入！", "提示", {
                  confirmButtonText: "确定",
                });
              }

            } else {
              let putData = {
                entMethodItemId: this.form.entMethodItemId,
                itemName: this.form.itemName,
                itemRemark: this.form.itemRemark,
                scoreLevel: this.form.scoreLevel,
                score: this.form.score,
              };
              updateUitem(putData).then((response) => {
                if (response.code === 200) {
                  this.dialogVisible = false;
                  this.$message.success("修改成功");
                  this.refreshTable();
                  this.$emit("saveSuccess", response.data);
                }
              });
            }

            this.dialogVisible = false;
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      } else {
        console.error("Form ref is undefined");
      }
    },
    
    /**
     * 计算总分
     * @returns {number} 总分
     */
    calculateTotalScore() {
      if (!this.itemInfos || this.itemInfos.length === 0) {
        return this.tempItem ? (this.tempItem.score || 0) : 0;
      }
      
      // 计算所有评分项的分值总和
      const totalScore = this.itemInfos.reduce((sum, item) => {
        const score = parseFloat(item.score) || 0;
        return sum + score;
      }, 0);
      
      return totalScore;
    },
  },
};
</script>

<style lang="scss" scoped>
.delete-icon {
  width: 25px;
  height: 25px;
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-itemremark: center; /* 垂直居中 */
}
.transfer {
  width: 90%;
  margin-bottom: 20px; /* 添加底部间距，使 transfer 和按钮之间有一定的距离 */
}
.item {
  display: flex;
  justify-itemremark: center;
  align-items: center;
  font-size: 18px;
}
.item-button {
  border: #333 1px solid;
  color: #fff;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #fff;
  }
}
</style>
