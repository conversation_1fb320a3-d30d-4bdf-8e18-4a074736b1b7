package com.ruoyi.eval.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.aspose.words.FontSettings;
import com.aspose.words.License;
import com.aspose.words.PdfFontEmbeddingMode;
import com.aspose.words.PdfSaveOptions;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.Rows;
import com.deepoove.poi.data.TableRenderData;
import com.deepoove.poi.data.Tables;
import com.ruoyi.busi.domain.*;
import com.ruoyi.busi.service.*;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.eval.domain.*;
import com.ruoyi.eval.mapper.EvalProjectEvaluationInfoMapper;
import com.ruoyi.eval.service.*;
import com.ruoyi.eval.vo.EvaluationResultVo;
import com.ruoyi.eval.vo.GetEvalAgainQuoteVo;
import com.ruoyi.scoring.domain.ScoringMethodItem;
import com.ruoyi.scoring.domain.ScoringMethodUinfo;
import com.ruoyi.scoring.domain.ScoringMethodUitem;
import com.ruoyi.scoring.service.IScoringMethodItemService;
import com.ruoyi.scoring.service.IScoringMethodUinfoService;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import com.ruoyi.scoring.util.ExcelStyleHelper;
import com.ruoyi.utils.AttachmentUtil;
import com.ruoyi.utils.ZhuanJiaInfoVo;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目评审信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class EvalProjectEvaluationInfoServiceImpl extends ServiceImpl<EvalProjectEvaluationInfoMapper, EvalProjectEvaluationInfo> implements IEvalProjectEvaluationInfoService {

    @Autowired
    private IEvalProjectEvaluationInfoService evalProjectEvaluationInfoService;
    @Autowired
    private IBusiExtractExpertResultService extractExpertResultService;
    @Autowired
    private IBusiWinningBidderNoticeService iBusiWinningBidderNoticeService;

    @Autowired
    private  IEvalProjectEvaluationProcessService iEvalProjectEvaluationProcessService;

    @Value("${spring.profiles.active}")
    private String active;
    @Autowired
    private IEvalExpertEvaluationInfoService evalExpertEvaluationInfoService;
    @Autowired
    private IBusiExtractExpertResultService busiExtractExpertResultService;
    @Autowired
    private IBusiExtractExpertApplyService busiExtractExpertApplyService;
    @Autowired
    private IScoringMethodItemService scoringMethodItemService;
    @Autowired
    IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IScoringMethodUinfoService scoringMethodUinfoService;
    @Autowired
    private IScoringMethodUitemService scoringMethodUitemService;
    @Autowired
    private IEvalExpertEvaluationDetailService evalExpertEvaluationDetailService;
    @Autowired
    private IEvalAgainQuoteService evalAgainQuoteService;
    @Autowired
    private IBusiTenderNoticeService busiTenderNoticeService;
    @Autowired
    private IBusiBiddingRecordService iBusiBiddingRecordService;
    @Autowired
    private IBusiTenderProjectService iBusiTenderProjectService;
    @Autowired
    private IBusiBidEvaluationService busiBidEvaluationService;
    @Autowired
    private IEvalExpertScoreInfoService iEvalExpertScoreInfoService;
    @Autowired
    IEvalProjectEvaluationInfoService iEvalProjectEvaluationInfoService;
    @Autowired
    private IBusiAttachmentService busiAttachmentService;

    @Value("${extractcode.zhuanJiaInfo}")
    String zhuanJiaInfo;
    @Value("${extractcode.url}")
    String tokenUrl;
    @Value("${extractcode.username}")
    String username;
    @Value("${extractcode.thirdPartySecret}")
    String thirdPartySecret;
    @Value("${extractcode.password}")
    String password;
    @Autowired
    private IBusiVenueOccupyService busiVenueOccupyService;

    /**
     * 查询项目评审信息列表
     *
     * @param evalProjectEvaluationInfo 项目评审信息
     * @return 项目评审信息
     */
    @Override
    public List<EvalProjectEvaluationInfo> selectList(EvalProjectEvaluationInfo evalProjectEvaluationInfo) {
        QueryWrapper<EvalProjectEvaluationInfo> evalProjectEvaluationInfoQueryWrapper = new QueryWrapper<>();
        evalProjectEvaluationInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalProjectEvaluationInfo.getProjectEvaluationId()), "project_evaluation_id", evalProjectEvaluationInfo.getProjectEvaluationId());
        evalProjectEvaluationInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalProjectEvaluationInfo.getProjectId()), "project_id", evalProjectEvaluationInfo.getProjectId());
        evalProjectEvaluationInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalProjectEvaluationInfo.getEvaluationTime()), "evaluation_time", evalProjectEvaluationInfo.getEvaluationTime());
        evalProjectEvaluationInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalProjectEvaluationInfo.getEvaluationEndTime()), "evaluation_end_time", evalProjectEvaluationInfo.getEvaluationEndTime());
        evalProjectEvaluationInfoQueryWrapper.apply(
                ObjectUtil.isNotEmpty(evalProjectEvaluationInfo.getParams().get("dataScope")),
                evalProjectEvaluationInfo.getParams().get("dataScope") + ""
        );
        return list(evalProjectEvaluationInfoQueryWrapper);
    }


    @Transactional
    @Override
    public AjaxResult saveEvalProjectEvaluationInfo(EvalProjectEvaluationInfo evalProjectEvaluationInfo) {
        EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", evalProjectEvaluationInfo.getProjectId()));
        if (null == evaluationInfo) {
            // 对象为空
            evalProjectEvaluationInfo.setEvaluationTime(new Date());
            evalProjectEvaluationInfoService.save(evalProjectEvaluationInfo);
        } else {
            // 对象不为空
//            return AjaxResult.error("项目评审信息已存在");
            evalProjectEvaluationInfo.setProjectEvaluationId(evaluationInfo.getProjectEvaluationId());
        }
        try {
            EvalExpertEvaluationInfo query2 = new EvalExpertEvaluationInfo();
            query2.setProjectId(Long.valueOf(evalProjectEvaluationInfo.getProjectId()));
            query2.setExpertCode(evalProjectEvaluationInfo.getExpertCode());
            AjaxResult qr = evalExpertEvaluationInfoService.getInfo2(query2);
            if (qr == null || qr.get("data") == null) {
                BusiExtractExpertResult busiExtractExpertResult = new BusiExtractExpertResult();
                busiExtractExpertResult.setProjectId(Long.valueOf(evalProjectEvaluationInfo.getProjectId()));
                List<BusiExtractExpertResult> expertList = extractExpertResultService.getZhuanJiaByProjectId(busiExtractExpertResult);
                BusiExtractExpertResult result = null;
                for (BusiExtractExpertResult r : expertList) {
                    if (r.getExpertCode().equals(evalProjectEvaluationInfo.getExpertCode())) {
                        result = r;
                        break;
                    }
                }
                if (result != null) {
                    EvalExpertEvaluationInfo evalExpertEvaluationInfo = new EvalExpertEvaluationInfo();
                    evalExpertEvaluationInfo.setEvalNode(1);
                    evalExpertEvaluationInfo.setExpertResultId(result.getResultId());
                    evalExpertEvaluationInfo.setProjectEvaluationId(evalProjectEvaluationInfo.getProjectEvaluationId());
                    evalExpertEvaluationInfoService.save(evalExpertEvaluationInfo);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.success(evalProjectEvaluationInfo);
    }
    @SuppressWarnings("unchecked")
    private static Map<String, Double> convertObjectToMap(Object object) {
        if (object instanceof Map<?,?>) {
            Map<String, Double> result = new HashMap<>();
            Map<?,?> map = (Map<?,?>) object;
            DecimalFormat decimalFormat = new DecimalFormat("#.00");
            for (Object key : map.keySet()) {
                if (key instanceof String) {
                    Object value = map.get(key);
                    if (value instanceof Double) {
                        double roundedValue = Double.parseDouble(decimalFormat.format((Double) value));
                        result.put((String) key, roundedValue);
                    }
                }
            }
            return result;
        }
        return null;
    }


    @Transactional
    @Override
    public void saveReviewReport(Long projectId, Long resultId, HttpServletResponse httpServletResponse) {

        Map<String, Object> dataMap = new HashMap<>();
        //公告类型，1中标结果公告 2变更公告 3流标公告
        BusiWinningBidderNotice bidderNoticeServiceOne= iBusiWinningBidderNoticeService.getOne(
                new QueryWrapper<BusiWinningBidderNotice>()
                        .eq("project_id", projectId)
                        .eq("notice_type",3)
                        .eq("del_flag",0)
        );
        BusiTenderProject project = iBusiTenderProjectService.getById(projectId);
        dataMap.put("project", project);
        dataMap.put("currentTimeow", new SimpleDateFormat("yyyy年MM月dd日").format(new Date()));
        BusiTenderNotice tenderNoticeByProjectId = busiTenderNoticeService.getTenderNoticeByProjectId(projectId);
        dataMap.put("bidEvaluationTime", new SimpleDateFormat("yyyy年MM月dd日HH时mm分").format(tenderNoticeByProjectId.getBidEvaluationTime()));

        // 获取此项目的所有供应商
        List<BusiBidderInfo> busiBidderInfoList = busiBidderInfoService.list(
                new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId)
        );

        // 从所有供应商中过滤出“有效”供应商（decode_flag=1 且 is_abandoned_bid=0）
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoList.stream()
                .filter(bidder ->
                        // 过滤条件：decode_flag=1 且 is_abandoned_bid=0
                        bidder.getDecodeFlag() == 1
                                && bidder.getIsAbandonedBid() == 0
                )
                .collect(Collectors.toList());

        List<Long> bidderIds = busiBidderInfoList.stream()
                .map(BusiBidderInfo::getBidderId)
                .collect(Collectors.toList());

        List<BusiBiddingRecord> records = iBusiBiddingRecordService.list(new QueryWrapper<BusiBiddingRecord>().eq("project_id", projectId).in("bidder_id",bidderIds));

        TableRenderData recordsTable = Tables.ofA4ExtendWidth().center().create();
        RowRenderData header = Rows.of("序号", "响应人").center().create();
        recordsTable.addRow(header);
        for (int i = 0; i < records.size(); i++) {
            //RowRenderData row1 = Rows.create(String.valueOf(i), records.get(i).getBidderName());
            RowRenderData row1 = Rows.of(String.valueOf(i+1), records.get(i).getBidderName()).center().create();
            recordsTable.addRow(row1);
        }
        dataMap.put("biddingRecords", recordsTable);
        dataMap.put("recordNum", records.size());
        List<BusiVenueOccupy> busiVenueOccupies = busiVenueOccupyService.list(new QueryWrapper<BusiVenueOccupy>()
                .eq("notice_id", tenderNoticeByProjectId.getNoticeId())
                .eq("venue_type", 2)
                .eq("del_flag", 0)
        );
        dataMap.put("occupyVenueNname", busiVenueOccupies.get(0).getVenueName());
        BusiExtractExpertApply extractExpertApply = busiExtractExpertApplyService.getOne(new QueryWrapper<BusiExtractExpertApply>().eq("project_id", projectId));
        if (extractExpertApply.getApplyMethod() == 0) {
            dataMap.put("applyMethod", "随机抽取");
        } else {
            dataMap.put("applyMethod", "自助抽取");
        }
        dataMap.put("expertNumber", extractExpertApply.getExpertNumber());

        List<BusiExtractExpertResult> busiExtractExpertResults = busiExtractExpertResultService.list(new QueryWrapper<BusiExtractExpertResult>()
                .eq("apply_id", extractExpertApply.getApplyId())
        );

        TableRenderData resultTable = Tables.ofA4ExtendWidth().center().create();
        RowRenderData resultHeader = Rows.of("姓名", "工作单位").center().create();
        resultTable.addRow(resultHeader);
        for (int i = 0; i < busiExtractExpertResults.size(); i++) {
            RowRenderData row1 = Rows.of(busiExtractExpertResults.get(i).getExpertName(), busiExtractExpertResults.get(i).getCompany()).center().create();
            // RowRenderData row1 = Rows.create(busiExtractExpertResults.get(i).getExpertName(), busiExtractExpertResults.get(i).getCompany());
            resultTable.addRow(row1);
        }
        dataMap.put("expertResults", resultTable);
    //tenderMode：1磋商3询价0谈判4单一来源  项目类别:0工程，1服务，2货物
        if (project.getTenderMode().equals("1")){

            if (bidderNoticeServiceOne != null) {
                dataMap.put("zbggzt", bidderNoticeServiceOne.getRemark());

                EvalProjectEvaluationInfo evaluationInfoServiceOne = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
                List<EvalExpertScoreInfo> expertScoreInfos = iEvalExpertScoreInfoService.list(
                        new QueryWrapper<EvalExpertScoreInfo>()
                                .eq("project_evaluation_id", evaluationInfoServiceOne.getProjectEvaluationId())
                );
//            Map<Long, String> expertScoreInfoMap = expertScoreInfos.stream()
//                    .collect(Collectors.toMap(EvalExpertScoreInfo::getExpertResultId, EvalExpertScoreInfo::getEvalContent));
                Map<Long, String> expertScoreInfoMap = expertScoreInfos.stream()
                        // 使用toMap的重载方法，传入合并函数来处理键重复时的合并逻辑
                        .collect(Collectors.toMap(
                                EvalExpertScoreInfo::getExpertResultId,
                                info -> info.getEvalContent() == null? "" : info.getEvalContent(),
                                (existingValue, newValue) -> existingValue + "," + newValue
                        ));
                StringBuffer bjjgsb=new StringBuffer();
                for (BusiExtractExpertResult extractExpertResult : busiExtractExpertResults) {
                    if(StringUtils.isNoneBlank(expertScoreInfoMap.get(extractExpertResult.getResultId()))) {
                        bjjgsb.append(extractExpertResult.getExpertName() + "：" + expertScoreInfoMap.get(extractExpertResult.getResultId()) + "\n");
                    }
                }
                dataMap.put("bjjgsb", bjjgsb);
                dataMap.put("dlt", "废标原因：合格供应商不足3家");
               // dataMap.put("dlt", "经磋商小组讨论，一致同意推荐：");

            }
            else {
                dataMap.put("zbggzt", "无");
                dataMap.put("dlt", "经磋商小组讨论，一致同意推荐：");
                //查询导出行所需要的数据信息
                ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>()
                        .eq("project_id", projectId));

                //审计行标题
                List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                        .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                        //.eq("item_mode", "2")
                        .eq("item_code", "tbbjdf")
                );
                //获取供应商的总分
                Map<Long,String> zfMap=new HashMap<>();

                Map<String, Object> reviewSummary = evalExpertEvaluationDetailService.getReviewSummary(projectId, resultId);
                Map<Long, Map<Long, String>> entZFMap= (Map<Long, Map<Long, String>>) reviewSummary.get("resultMap");
                List<Long> xwqy = new ArrayList<>();
                for (BusiBidderInfo busiBidderInfo : busiBidderInfos) {
                    EvalExpertEvaluationDetail evalExpertEvaluationDetail = evalExpertEvaluationDetailService.getOne(new QueryWrapper<EvalExpertEvaluationDetail>()
                            .eq("scoring_method_uitem_id", scoringMethodItems.get(0).getScoringMethodItemId())
                            .eq("expert_result_id", resultId)
                            .eq("ent_id", busiBidderInfo.getBidderId())
                            .eq("remark", "xwqy")
                    );
                    if (null != evalExpertEvaluationDetail) {
                        xwqy.add(busiBidderInfo.getBidderId());
                    }
                    Map<Long, String> objectData = entZFMap.get(busiBidderInfo.getBidderId());
                    zfMap.put(busiBidderInfo.getBidderId(),objectData.get(1l));
//                zfMap.put(busiBidderInfo.getBidderId(),)
                }
                GetEvalAgainQuoteVo getEvalAgainQuoteVo = new GetEvalAgainQuoteVo();
                getEvalAgainQuoteVo.setItemId(scoringMethodItems.get(0).getScoringMethodItemId());
                getEvalAgainQuoteVo.setResultId(resultId);
                getEvalAgainQuoteVo.setProjectId(projectId);
                getEvalAgainQuoteVo.setXwqys(xwqy);

                System.out.println(JSONObject.toJSONString(reviewSummary));


                Map<Long, Map<String, BigDecimal>> fenzhiTableData = (Map<Long, Map<String, BigDecimal>>) reviewSummary.get("resultMap");
                System.out.println(JSONObject.toJSONString(fenzhiTableData));

                List<Map.Entry<Long, Map<String, BigDecimal>>> sortedList = new ArrayList<>(fenzhiTableData.entrySet());
                sortedList.sort((entry1, entry2) -> {
                    // 获取嵌套 Map 中 key="1" 的值
                    Object value = entry1.getValue().get(1L);
                    if (value != null) {
                        System.out.println("实际类型：" + value.getClass().getName());
                        BigDecimal bigDecimal = new BigDecimal(entry1.getValue().get(1L)+"");
                        System.out.println("实际类型：" + bigDecimal.getClass().getName());

                    }
                    String str1= entry1.getValue().get(1L)+"";
                    String str2= entry2.getValue().get(1L)+"";
                    BigDecimal val1 =new BigDecimal(str1);
                    BigDecimal val2 = new BigDecimal(str2);

                    // 降序排序（val2 在前，val1 在后）
                    return val2.compareTo(val1);
                });
               /* Map<String, Object> resultMap = evalAgainQuoteService.getEvalAgainQuote(getEvalAgainQuoteVo);
                Map<Long, Map<String, BigDecimal>> tableData = (Map<Long, Map<String, BigDecimal>>) resultMap.get("resultMap");

                // 将Map转换为List
                List<Map.Entry<Long, Map<String, BigDecimal>>> sortedList = new ArrayList<>(tableData.entrySet());

                // 对列表进行降序排序
                sortedList.sort((entry1, entry2) -> {
                    BigDecimal value1 = entry1.getValue().get("投标报价得分");
                    BigDecimal value2 = entry2.getValue().get("投标报价得分");
                    // 注意比较参数的顺序，这里实现了降序排序
                    return value2.compareTo(value1);
                });*/
                TableRenderData tbbjdfTable = Tables.ofA4ExtendWidth().center().create();
                RowRenderData tbbjdfHeader = Rows.of("序号", "响应人名称", "最终报价（元）", "综合得分", "排名").center().create();
                tbbjdfTable.addRow(tbbjdfHeader);
                for (int i = 0; i < sortedList.size(); i++) {
                    Map.Entry<Long, Map<String, BigDecimal>> entry = sortedList.get(i);
                    Long bidderId = entry.getKey();
                    Map<String, BigDecimal> decimalMap = entry.getValue();
                    // 查找对应的BusiBidderInfo对象
                    BusiBidderInfo bidderInfo = busiBidderInfos.stream()
                            .filter(b -> b.getBidderId().equals(bidderId))
                            .findFirst()
                            .orElse(null);

                    if (bidderInfo != null) {
                        RowRenderData row = Rows.of(
                                String.valueOf(i + 1), // 序号从1开始
                                bidderInfo.getBidderName(),
                                decimalMap.get(1L)+"",
                                //decimalMap.get("投标报价得分").toString(),
                                zfMap.get(bidderInfo.getBidderId()),
                                // reviewSummary.get(bidderInfo.getBidderId());
                                String.valueOf(i + 1) // 排名与序号相同
                        ).center().create();
                        tbbjdfTable.addRow(row);
                    }
                }
                dataMap.put("tbbjdf", tbbjdfTable);
                dataMap.put("recommendedCandidatesOne", "第一成交候选人为："+busiBidderInfos.stream()
                        .filter(b -> b.getBidderId().equals(sortedList.get(0).getKey()))
                        .findFirst()
                        .orElse(null).getBidderName());

                dataMap.put("recommendedCandidatesTwo", "第二成交候选人为："+busiBidderInfos.stream()
                        .filter(b -> b.getBidderId().equals(sortedList.get(1).getKey()))
                        .findFirst()
                        .orElse(null).getBidderName());


                dataMap.put("recommendedCandidatesThree", "第三成交候选人为："+busiBidderInfos.stream()
                        .filter(b -> b.getBidderId().equals(sortedList.get(2).getKey()))
                        .findFirst()
                        .orElse(null).getBidderName());
                // dataMap.put("zbggzt", "无");
                EvalProjectEvaluationInfo evaluationInfoServiceOne = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
                List<EvalExpertScoreInfo> expertScoreInfos = iEvalExpertScoreInfoService.list(
                        new QueryWrapper<EvalExpertScoreInfo>()
                                .eq("project_evaluation_id", evaluationInfoServiceOne.getProjectEvaluationId())
                );
//            Map<Long, String> expertScoreInfoMap = expertScoreInfos.stream()
//                    .collect(Collectors.toMap(EvalExpertScoreInfo::getExpertResultId, EvalExpertScoreInfo::getEvalContent));
                Map<Long, String> expertScoreInfoMap = expertScoreInfos.stream()
                        // 使用toMap的重载方法，传入合并函数来处理键重复时的合并逻辑
                        .collect(Collectors.toMap(
                                EvalExpertScoreInfo::getExpertResultId,
                                info -> info.getEvalContent() == null? "" : info.getEvalContent(),
                                (existingValue, newValue) -> existingValue + "," + newValue
                        ));
                StringBuffer bjjgsb=new StringBuffer();
                for (BusiExtractExpertResult extractExpertResult : busiExtractExpertResults) {
                    if(StringUtils.isNoneBlank(expertScoreInfoMap.get(extractExpertResult.getResultId()))) {
                        bjjgsb.append(extractExpertResult.getExpertName() + "：" + expertScoreInfoMap.get(extractExpertResult.getResultId()) + "\n");
                    }
                }
                dataMap.put("bjjgsb", bjjgsb);
                String templatePath = RuoYiConfig.getProfile() + "/templates/磋商评标报告.docx";
                String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + projectId;
                String originPath = s1 + "/磋商评标报告.docx";
                String genPath = s1 + "/磋商评标报告.pdf";
                FileUploadUtils.checkDirExists(s1);
                try {
                    XWPFTemplate.compile(templatePath)
                            .render(dataMap)
                            .writeToFile(originPath);
                    String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
                    byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
                    ByteArrayInputStream is = new ByteArrayInputStream(bytes);
                    License license = new License();
                    license.setLicense(is);
                    // 指定Linux系统上的中文字体目录
//            String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
//            FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
                    if (active.equals("release")){
                        // 指定Linux系统上的中文字体目录
                        String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                        FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
                    }
                    com.aspose.words.Document document = new com.aspose.words.Document(originPath);
                    System.out.println("PDF文件地址：" + genPath);
                    // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
                    PdfSaveOptions saveOptions = new PdfSaveOptions();
                    saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
                    document.save(genPath, saveOptions);

                    // 创建一个字节数组输出流
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    // 将文档保存到字节数组输出流
                    document.save(byteArrayOutputStream, saveOptions);

                    // 获取字节数组输出流的内容作为字节数组
                    byte[] pdfBytes = byteArrayOutputStream.toByteArray();

                    // 设置响应头信息
                    httpServletResponse.setContentType("application/pdf");

                    // 对文件名进行URL编码
                    String encodedFilename = URLEncoder.encode("评标报告.pdf", "UTF-8");
                    httpServletResponse.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename);
                    httpServletResponse.setContentLength(pdfBytes.length);

                    // 将PDF内容写入到响应输出流
                    try (OutputStream outputStream = httpServletResponse.getOutputStream()) {
                        outputStream.write(pdfBytes);
                        outputStream.flush();
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
               // dataMap.put("pszjbjyj", "无");

             /*   String templatePath = RuoYiConfig.getProfile() + "/templates/磋商评标报告.docx";
                String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + projectId;
                String originPath = s1 + "/磋商评标报告.docx";
                String genPath = s1 + "/磋商评标报告.pdf";
                FileUploadUtils.checkDirExists(s1);
                try {
                    XWPFTemplate.compile(templatePath)
                            .render(dataMap)
                            .writeToFile(originPath);
                    String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
                    byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
                    ByteArrayInputStream is = new ByteArrayInputStream(bytes);
                    License license = new License();
                    license.setLicense(is);
                    // 指定Linux系统上的中文字体目录
//            String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
//            FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
                    if (active.equals("release")){
                        // 指定Linux系统上的中文字体目录
                        String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                        FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
                    }
                    com.aspose.words.Document document = new com.aspose.words.Document(originPath);
                    System.out.println("PDF文件地址：" + genPath);
                    // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
                    PdfSaveOptions saveOptions = new PdfSaveOptions();
                    saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
                    document.save(genPath, saveOptions);

                    // 创建一个字节数组输出流
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    // 将文档保存到字节数组输出流
                    document.save(byteArrayOutputStream, saveOptions);

                    // 获取字节数组输出流的内容作为字节数组
                    byte[] pdfBytes = byteArrayOutputStream.toByteArray();

                    // 设置响应头信息
                    httpServletResponse.setContentType("application/pdf");

                    // 对文件名进行URL编码
                    String encodedFilename = URLEncoder.encode("评标报告.pdf", "UTF-8");
                    httpServletResponse.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename);
                    httpServletResponse.setContentLength(pdfBytes.length);

                    // 将PDF内容写入到响应输出流
                    try (OutputStream outputStream = httpServletResponse.getOutputStream()) {
                        outputStream.write(pdfBytes);
                        outputStream.flush();
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }*/
            }
            else if (project.getTenderMode().equals("0")||project.getTenderMode().equals("3")||project.getTenderMode().equals("4")){
            if (bidderNoticeServiceOne != null) {
                dataMap.put("zbggzt", bidderNoticeServiceOne.getRemark());
                EvalProjectEvaluationInfo evaluationInfoServiceOne = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
                List<EvalExpertScoreInfo> expertScoreInfos = iEvalExpertScoreInfoService.list(
                        new QueryWrapper<EvalExpertScoreInfo>()
                                .eq("project_evaluation_id", evaluationInfoServiceOne.getProjectEvaluationId())
                );
                Map<Long, String> expertScoreInfoMap = expertScoreInfos.stream()
                        // 使用toMap的重载方法，传入合并函数来处理键重复时的合并逻辑
                        .collect(Collectors.toMap(
                                EvalExpertScoreInfo::getExpertResultId,
                                info -> info.getEvalContent() == null? "" : info.getEvalContent(),
                                (existingValue, newValue) -> existingValue + "," + newValue
                        ));
                StringBuffer bjjgsb=new StringBuffer();
                for (BusiExtractExpertResult extractExpertResult : busiExtractExpertResults) {
                    if(StringUtils.isNoneBlank(expertScoreInfoMap.get(extractExpertResult.getResultId()))) {
                        bjjgsb.append(extractExpertResult.getExpertName() + "：" + expertScoreInfoMap.get(extractExpertResult.getResultId()) + "\n");
                    }
                }
                dataMap.put("bjjgsb", bjjgsb);
                dataMap.put("dlt", "废标原因：合格供应商不足3家");
            }else {
                dataMap.put("zbggzt", "无");
                dataMap.put("dlt", "经磋商小组讨论，一致同意推荐：");
                //查询导出行所需要的数据信息
                ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>()
                        .eq("project_id", projectId));

                //审计行标题
                List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                        .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                        //.eq("item_mode", "2")
                        .eq("item_code", "tbbjdf")
                );
                //首次报价价格
                List<BusiBiddingRecord> biddingRecords = iBusiBiddingRecordService.list(
                        new QueryWrapper<BusiBiddingRecord>()
                                .eq("project_id", projectId)
                                .eq("del_flag", 0)
                                .orderByAsc("bid_amount")
                );
                Map<Long, BigDecimal> bidAmountMap = biddingRecords.stream()
                        .collect(Collectors.toMap(
                                BusiBiddingRecord::getBidderId, // Key: biddingId
                                BusiBiddingRecord::getBidAmount // Value: bidAmount
                        ));
                //供应商集合
               // List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));



                ScoringMethodUitem one = scoringMethodUitemService.getOne(new QueryWrapper<ScoringMethodUitem>()
                        .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                        .eq("scoring_method_item_id", scoringMethodItems.get(0).getScoringMethodItemId())
                        .eq("ent_method_id",scoringMethodUinfo.getEntMethodId())
                );

                //重新计算
                QueryWrapper<EvalExpertEvaluationDetail> queryWrapper=new QueryWrapper();
                queryWrapper.eq("scoring_method_uitem_id",one.getEntMethodItemId());
                queryWrapper.eq("expert_result_id",resultId);
//                queryWrapper.in("ent_id",bidderIds);
                queryWrapper.inSql("ent_id", "SELECT bidder_id FROM busi_bidder_info WHERE project_id="+projectId+" AND is_abandoned_bid=0");
                queryWrapper.orderByAsc("evaluation_result");
                List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails = evalExpertEvaluationDetailService.list(queryWrapper);
                if (evalExpertEvaluationDetails.size()>0){
                    // 对列表进行排序
                    Collections.sort(evalExpertEvaluationDetails, new Comparator<EvalExpertEvaluationDetail>() {
                        @Override
                        public int compare(EvalExpertEvaluationDetail o1, EvalExpertEvaluationDetail o2) {
                            // 假设evaluationResult是一个表示金额的字符串，例如 "$1,234.56"
                            // 需要移除货币符号和千位分隔符，然后转换为BigDecimal
                            BigDecimal amount1 = new BigDecimal(o1.getEvaluationResult().replaceAll("[^\\d.]", ""));
                            BigDecimal amount2 = new BigDecimal(o2.getEvaluationResult().replaceAll("[^\\d.]", ""));
                            return amount1.compareTo(amount2);
                        }
                    });

                    // 如果您使用Java 8或更高版本，可以使用lambda表达式简化代码
                    evalExpertEvaluationDetails.sort(Comparator.comparing(o -> new BigDecimal(o.getEvaluationResult().replaceAll("[^\\d.]", ""))));

                    TableRenderData tbbjdfTable = Tables.ofA4ExtendWidth().center().create();
                    RowRenderData tbbjdfHeader = Rows.of("序号", "响应人名称", "最终报价（元）","排名").center().create();
                    tbbjdfTable.addRow(tbbjdfHeader);

                    for (int i = 0; i < evalExpertEvaluationDetails.size(); i++) {
                        Long bidderId  = evalExpertEvaluationDetails.get(i).getEntId();
                        // 查找对应的BusiBidderInfo对象
                        BusiBidderInfo bidderInfo = busiBidderInfos.stream()
                                .filter(b -> b.getBidderId().equals(bidderId))
                                .findFirst()
                                .orElse(null);

                        if (bidderInfo != null) {
                            RowRenderData row = Rows.of(
                                    String.valueOf(i + 1), // 序号从1开始
                                    bidderInfo.getBidderName(),
                                    bidAmountMap.get(bidderId).toString(),
                                    //  zfMap.get(bidderInfo.getBidderId()),
                                    String.valueOf(i + 1) // 排名与序号相同
                            ).center().create();
                            tbbjdfTable.addRow(row);
                        }
                    }

                    dataMap.put("tbbjdf", tbbjdfTable);

// 直接使用已正确排序的 evalExpertEvaluationDetails 列表
                    List<EvalExpertEvaluationDetail> topThree = evalExpertEvaluationDetails.stream()
                            .limit(3)
                            .collect(Collectors.toList());

// 判断前两名评分是否相同（示例逻辑，需根据实际业务调整）
                    boolean isFirstTwoEqual = topThree.size() >= 2 &&
                            topThree.get(0).getEvaluationResult().equals(topThree.get(1).getEvaluationResult());

// 设置推荐候选人
                    if (isFirstTwoEqual) {
                        // 并列第一名的处理
                        dataMap.put("recommendedCandidatesOne",
                                getBidderName(topThree.get(0).getEntId(), busiBidderInfos) + ", " +
                                        getBidderName(topThree.get(1).getEntId(), busiBidderInfos)
                        );
                        if (topThree.size() >= 3) {
                            dataMap.put("recommendedCandidatesTwo",
                                    getBidderName(topThree.get(2).getEntId(), busiBidderInfos)
                            );
                        }
                    } else {
                        // 正常排名处理
                        dataMap.put("recommendedCandidatesOne", "第一成交候选人为："+getBidderName(topThree.get(0).getEntId(), busiBidderInfos));
                        dataMap.put("recommendedCandidatesTwo", "第二成交候选人为："+getBidderName(topThree.get(1).getEntId(), busiBidderInfos));
                        if (topThree.size() >= 3) {
                            dataMap.put("recommendedCandidatesThree", "第三成交候选人为："+getBidderName(topThree.get(2).getEntId(), busiBidderInfos));
                        }
                    }
                }

                EvalProjectEvaluationInfo evaluationInfoServiceOne = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
                List<EvalExpertScoreInfo> expertScoreInfos = iEvalExpertScoreInfoService.list(
                        new QueryWrapper<EvalExpertScoreInfo>()
                                .eq("project_evaluation_id", evaluationInfoServiceOne.getProjectEvaluationId())
                );
                Map<Long, String> expertScoreInfoMap = expertScoreInfos.stream()
                        // 使用toMap的重载方法，传入合并函数来处理键重复时的合并逻辑
                        .collect(Collectors.toMap(
                                EvalExpertScoreInfo::getExpertResultId,
                                info -> info.getEvalContent() == null? "" : info.getEvalContent(),
                                (existingValue, newValue) -> existingValue + "," + newValue
                        ));
                StringBuffer bjjgsb=new StringBuffer();
                for (BusiExtractExpertResult extractExpertResult : busiExtractExpertResults) {
                    if(StringUtils.isNoneBlank(expertScoreInfoMap.get(extractExpertResult.getResultId()))) {
                        bjjgsb.append(extractExpertResult.getExpertName() + "：" + expertScoreInfoMap.get(extractExpertResult.getResultId()) + "\n");
                    }
                }
                dataMap.put("bjjgsb", bjjgsb);
            }

               // dataMap.put("pszjbjyj", "无");
                String templatePath = RuoYiConfig.getProfile() + "/templates/询价评标报告.docx";
                String s1 = RuoYiConfig.getUploadPath() + "/procurement/" + projectId;
                String originPath = s1 + "/询价评标报告.docx";
                String genPath = s1 + "/询价评标报告.pdf";
                FileUploadUtils.checkDirExists(s1);
                try {
                    XWPFTemplate.compile(templatePath)
                            .render(dataMap)
                            .writeToFile(originPath);
                    String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
                    byte[] bytes = s.getBytes(StandardCharsets.UTF_8); // 使用UTF-8编码
                    ByteArrayInputStream is = new ByteArrayInputStream(bytes);
                    License license = new License();
                    license.setLicense(is);
                    // 指定Linux系统上的中文字体目录
//            String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
//            FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
                    if (active.equals("release")){
                        // 指定Linux系统上的中文字体目录
                        String chineseFontsPath = RuoYiConfig.getProfile() + "/fonts";
                        FontSettings.setFontsFolder(chineseFontsPath, true); // 添加到现有的字体搜索路径中
                    }
                    com.aspose.words.Document document = new com.aspose.words.Document(originPath);
                    System.out.println("PDF文件地址：" + genPath);
                    // 配置保存为PDF时的字体嵌入等选项，以确保最佳兼容性和显示效果
                    PdfSaveOptions saveOptions = new PdfSaveOptions();
                    saveOptions.setFontEmbeddingMode(PdfFontEmbeddingMode.EMBED_ALL); // 尝试嵌入字体到PDF文档中
                    document.save(genPath, saveOptions);

                    // 创建一个字节数组输出流
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    // 将文档保存到字节数组输出流
                    document.save(byteArrayOutputStream, saveOptions);

                    // 获取字节数组输出流的内容作为字节数组
                    byte[] pdfBytes = byteArrayOutputStream.toByteArray();

                    // 设置响应头信息
                    httpServletResponse.setContentType("application/pdf");

                    // 对文件名进行URL编码
                    String encodedFilename = URLEncoder.encode("评标报告.pdf", "UTF-8");
                    httpServletResponse.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFilename);
                    httpServletResponse.setContentLength(pdfBytes.length);

                    // 将PDF内容写入到响应输出流
                    try (OutputStream outputStream = httpServletResponse.getOutputStream()) {
                        outputStream.write(pdfBytes);
                        outputStream.flush();
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
       // }


    }
    // 辅助方法：通过 bidderId 获取供应商名称
    private String getBidderName(Long bidderId, List<BusiBidderInfo> bidderInfos) {
        return bidderInfos.stream()
                .filter(b -> b.getBidderId().equals(bidderId))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("未找到供应商信息"))
                .getBidderName();
    }
    @Override
    public void updateInfos(EvalProjectEvaluationInfo evalProjectEvaluationInfo) {
        if(updateById(evalProjectEvaluationInfo)){
            for (BusiBidderInfo info : evalProjectEvaluationInfo.getBidderInfos()) {
                EvalAgainQuote query = new EvalAgainQuote();
                query.setProjectId(evalProjectEvaluationInfo.getProjectId());
                query.setEntId(info.getBidderId());

                //
              BusiTenderProject byId = iBusiTenderProjectService.getById(evalProjectEvaluationInfo.getProjectId());
                if (byId.getTenderMode().equals("1")){
                    BigDecimal amount = evalAgainQuoteService.getEntAmount(query);
                    info.setBidderAmount(amount);
                }else {
                    info.setBidderAmount(info.getScore());
                }
                busiBidderInfoService.updateById(info);
            }
            //评审结束增加修改项目状态
            if (null!=evalProjectEvaluationInfo.getProjectId()){
                BusiTenderProject byId = iBusiTenderProjectService.getById(evalProjectEvaluationInfo.getProjectId());
                byId.setProjectStatus(50);
                iBusiTenderProjectService.updateById(byId);
            }
        }
    }

    @Override
    public EvalProjectEvaluationInfo selectByProject(Long projectId) {
        EvalProjectEvaluationInfo query = new EvalProjectEvaluationInfo();
        query.setProjectId(projectId);
        List<EvalProjectEvaluationInfo> infoList = selectList(query);
        if (infoList == null && infoList.isEmpty()) {
            return null;
        } else if (infoList.size() > 1) {
            throw new RuntimeException("数据异常");
        }else{
            return infoList.get(0);
        }
    }

    @Transactional
    @Override
    public void exportEvalProjectEvaluationInfo(Long projectId, Long resultId, HttpServletResponse httpServletResponse) throws IOException {

        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        EvalProjectEvaluationInfo evaluationInfo = iEvalProjectEvaluationInfoService.selectByProject(projectId);
        //公告类型，1中标结果公告 2变更公告 3流标公告
        BusiWinningBidderNotice bidderNoticeServiceOne= iBusiWinningBidderNoticeService.getOne(
                new QueryWrapper<BusiWinningBidderNotice>()
                        .eq("project_id", projectId)
                        .eq("notice_type",3)
                        .eq("del_flag",0)
        );

        // 创建一个新的Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        // 评审专家签到表
        sheet1(projectId, workbook);
        //初步评审表
        sheet2(projectId, resultId, workbook);

        if (null==bidderNoticeServiceOne){
            //技术部分评审表
            //sheet3(projectId, resultId, workbook);
            //技术分汇总表 sheet4
            if (tenderProject.getTenderMode().equals("1")) {
                sheet4(projectId, resultId, workbook);
                //商务部分评审表
        //      sheet5(projectId, resultId, workbook);
                //综合得分汇总
                sheet6(projectId, resultId, workbook);
                //投标报价得分表
                sheet7(projectId, resultId, workbook);
                //评标汇总表
                sheet8(projectId, resultId, workbook);
            }else if (tenderProject.getTenderMode().equals("0")||tenderProject.getTenderMode().equals("3")||tenderProject.getTenderMode().equals("4")){
                sheet9(projectId, resultId, workbook);
            }
        }


        try {
            // 设置Excel文件名
            String fileName = "评审结果.xlsx";
            // 设置MIME类型
            httpServletResponse.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            httpServletResponse.setCharacterEncoding("utf-8");
            // 设置下载的文件名
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));

            String filePath = RuoYiConfig.getUploadPath() + "/procurement/" + tenderProject.getProjectId()+fileName;
            // 写入Excel文件
            // 保存Excel文件到服务器
            try (FileOutputStream fos = new FileOutputStream(filePath)) {
                workbook.write(fos);
                // 文件已保存到服务器
            } catch (IOException e) {
                // 处理异常，例如记录日志或抛出异常
                e.printStackTrace();
            }
            //因全流程归档需要，所以增加此保存附件记录
            List<BusiAttachment> attachments = busiAttachmentService.list(new QueryWrapper<BusiAttachment>().eq("busi_id", evaluationInfo.getProjectEvaluationId()));
            if (attachments.isEmpty()){
                BusiAttachment busiAttachment = new BusiAttachment();
                busiAttachment.setBusiId(evaluationInfo.getProjectEvaluationId());
                busiAttachment.setFilePath(AttachmentUtil.realToUrl(filePath));
                busiAttachment.setFileName(FileUtils.getName(filePath));
                String suffix = AttachmentUtil.getAttachmentSuffix(filePath);
                busiAttachment.setFileType("1");
                busiAttachment.setFileSuffix(suffix);
                busiAttachment.getParams().put("opUser", "保存评审记录");
                busiAttachmentService.save(busiAttachment);
            }
            try (OutputStream os = httpServletResponse.getOutputStream()) {
                workbook.write(os);
                // 不需要手动关闭OutputStream，try-with-resources会自动关闭
            }
            // 不需要手动关闭workbook，因为在上面的try块中已经处理了
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        System.out.println("结束了");
    }

    private static void setBorderStyle(CellStyle cellStyle, Workbook workbook) {
        // 设置边框样式为黑色细线
        short borderColor = IndexedColors.BLACK.getIndex();
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(borderColor);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(borderColor);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(borderColor);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setTopBorderColor(borderColor);
    }

    public String getLastPartOfString(String input) {
        // 检查输入字符串是否为空或null
        if (input == null || input.isEmpty()) {
            return input; // 或者你可以抛出一个异常或返回一个空字符串
        }

        // 找到最后一个“/”的位置
        int lastIndex = input.lastIndexOf("/");

        // 如果没有找到“/”，则返回整个字符串
        if (lastIndex == -1) {
            return input;
        }

        // 获取最后一个“/”之后的字符串
        return input.substring(lastIndex + 1);
    }

    public String getToken() throws IOException {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\r\n\"t\":" + System.currentTimeMillis() + ",\r\n\"username\":\"" + username + "\",\r\n\"password\":\"" + password + "\",\r\n\"thirdPartySecret\":\"" + thirdPartySecret + "\"\r\n}");

        System.out.println("{\r\n\"t\":" + System.currentTimeMillis() + ",\r\n\"username\":\"" + username + "\",\r\n\"password\":\"" + password + "\",\r\n\"thirdPartySecret\":\"" + thirdPartySecret + "\"\r\n}");
        Request request = new Request.Builder()
                .url(tokenUrl)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
        if (response.body() != null) {
            JSONObject jsonObject = JSON.parseObject(response.body().string());
            return jsonObject.getString("token");
        } else {
            throw new RuntimeException("请求专家抽取token 失败");
        }
    }

    //评审专家签到表
    public Sheet sheet1(Long projectId, Workbook workbook) throws IOException {
        //通过项目id查询项目信息、专家信息及开标记录表
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));


        List<BusiExtractExpertApply> busiExtractExpertApplies = busiExtractExpertApplyService.list(new QueryWrapper<BusiExtractExpertApply>().eq("project_id", projectId));
        // 使用流来提取projectId
        List<Long> applyIds = busiExtractExpertApplies.stream()
                .map(BusiExtractExpertApply::getApplyId)
                .collect(Collectors.toList());
        //获取不需要回避的专家信息，
        List<BusiExtractExpertResult> list = busiExtractExpertResultService.list(new QueryWrapper<BusiExtractExpertResult>().in("apply_id", applyIds).eq("is_avoid", 0));
        for (BusiExtractExpertResult busiExtractExpertResult : list) {
            if(busiExtractExpertResult.getIsOwner()==0) {
                OkHttpClient client = new OkHttpClient().newBuilder()
                        .build();
                Request request = new Request.Builder()
                        .url(zhuanJiaInfo + busiExtractExpertResult.getExpertId())
                        .method("GET", null)
                        .addHeader("Token", getToken())
                        .build();
                Response response = client.newCall(request).execute();
                ZhuanJiaInfoVo zhuanJiaInfoVo = JSONObject.parseObject(response.body().string(), ZhuanJiaInfoVo.class);
                busiExtractExpertResult.setZhuanJiaInfoVo(zhuanJiaInfoVo.getExpert());
            }
        }

        Sheet sheet = workbook.createSheet("评审专家签到表");
        // 创建字体
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 18);

        Font titleFont = workbook.createFont();
        titleFont.setFontHeightInPoints((short) 10);

        Font titleRowFont = workbook.createFont();
        titleRowFont.setBold(true);
        titleRowFont.setFontHeightInPoints((short) 14);

        // 创建单元格样式
        CellStyle headerCellStyle = workbook.createCellStyle();
        headerCellStyle.setFont(headerFont);
        headerCellStyle.setAlignment(HorizontalAlignment.CENTER);
        headerCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerCellStyle.setWrapText(true); // 开启自动换行

        setBorderStyle(headerCellStyle, workbook); // 设置边框样式

        CellStyle titleCellStyle = workbook.createCellStyle();
        titleCellStyle.setFont(titleFont);
        titleCellStyle.setAlignment(HorizontalAlignment.LEFT);
        titleCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleCellStyle.setWrapText(true); // 开启自动换行

        setBorderStyle(titleCellStyle, workbook); // 设置边框样式

        CellStyle contentCellStyle = workbook.createCellStyle();
        contentCellStyle.setFont(titleFont);
        contentCellStyle.setAlignment(HorizontalAlignment.CENTER);
        contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentCellStyle.setWrapText(true); // 开启自动换行

        setBorderStyle(contentCellStyle, workbook); // 设置边框样式

        CellStyle titleRowCellStyle = workbook.createCellStyle();
        titleRowCellStyle.setFont(titleRowFont);
        titleRowCellStyle.setAlignment(HorizontalAlignment.CENTER);
        titleRowCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleRowCellStyle.setWrapText(true); // 开启自动换行

        setBorderStyle(titleRowCellStyle, workbook); // 设置边框样式
        sheet.setColumnWidth(0, 10 * 256); // 第一列宽度
        sheet.setColumnWidth(1, 10 * 256); // 第二列宽度
        sheet.setColumnWidth(2, 20 * 256); // 第三列宽度
        sheet.setColumnWidth(3, 20 * 256); // 第四列宽度

        // 创建行并设置行高和内容
        for (int i = 0; (i < 3 + list.size()); i++) {
            Row row = sheet.createRow(i);
            row.setHeightInPoints(60); // 设置行高
            // 设置单元格边框样式
            for (int j = 0; j < 4; j++) {
                Cell cell = row.createCell(j);
                sheet.setColumnWidth(j, 15 * 256);

                if (i == 0) {
                    cell.setCellStyle(headerCellStyle);
                } else if (4 > i && i > 0) {
                    row.setHeightInPoints(40); // 设置行高
                    cell.setCellStyle(titleCellStyle);
                } else if (i == 4) {
                    cell.setCellStyle(titleRowCellStyle);
                } else {
                    row.setHeightInPoints(60); // 设置行高
                    cell.setCellStyle(titleRowCellStyle);
                }
                setBorderStyle(cell.getCellStyle(), workbook); // 设置单元格边框样式
            }
        }

        // 创建一个SimpleDateFormat对象，定义所需的日期时间格式
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日HH点mm分");
        // 使用formatter格式化Date对象
        String formattedDate = formatter.format(evaluationInfo.getEvaluationTime());
//        String excelTitle="项目名称：" +tenderProject.getProjectName()+"\r\n"+
//                "项目编号：" +tenderProject.getProjectCode()+"\r\n"+ "评标日期："+ formattedDate;

        // 合并第一行4个单元格，并设置内容
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
        sheet.getRow(0).getCell(0).setCellValue("评审专家签到表");

        // 合并第二行4个单元格，并设置内容
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 3));
        sheet.getRow(1).getCell(0).setCellValue("项目名称：" + tenderProject.getProjectName());

        // 合并第三行4个单元格，并设置内容
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 3));
        sheet.getRow(2).getCell(0).setCellValue("项目编号：" + tenderProject.getProjectCode());

        // 合并第四行4个单元格，并设置内容
        sheet.addMergedRegion(new CellRangeAddress(3, 3, 0, 3));
        sheet.getRow(3).getCell(0).setCellValue("评标日期：" + formattedDate);

        // 重新设置第三行第一个单元格的右侧边框，因为合并单元格会覆盖它
        Row titleRow = sheet.getRow(4);
        Cell firstCellOfTitleRow = titleRow.getCell(0);
        CellStyle firstCellStyle = firstCellOfTitleRow.getCellStyle();
        firstCellStyle.setBorderRight(BorderStyle.THIN);
        firstCellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());


        // 创建标题行
        String[] titles = {"序号", "姓名", "专业职称", "联系方式"};
        titleRow = sheet.getRow(4);
        for (int i = 0; i < titles.length; i++) {
            titleRow.getCell(i).setCellValue(titles[i]);
        }

        for (int i = 0; i < list.size(); i++) {
            Row dataRow = sheet.createRow(i + 5);
            BusiExtractExpertResult busiExtractExpertResult = list.get(i);
            dataRow.createCell(0).setCellValue((i + 1));
            dataRow.createCell(1).setCellValue(busiExtractExpertResult.getExpertName());
            if (null==busiExtractExpertResult.getZhuanJiaInfoVo()){
                dataRow.createCell(2).setCellValue("业主代表");
            }else {
                dataRow.createCell(2).setCellValue(
                        busiExtractExpertResult.getIsOwner()==0?getLastPartOfString(busiExtractExpertResult.getZhuanJiaInfoVo().getZc()):"业主代表");
            }

            dataRow.createCell(3).setCellValue(busiExtractExpertResult.getPhone());
            // 设置单元格样式
            for (int j = 0; j < 3; j++) {
                Cell cell = dataRow.getCell(j);
                cell.setCellStyle(contentCellStyle); // 使用标题行的样式
                setBorderStyle(cell.getCellStyle(), workbook); // 设置边框样式
            }

            Row sheetRow = sheet.getRow(i + 5);
            Cell Dfe = sheetRow.getCell(0);
            CellStyle cellStyle = Dfe.getCellStyle();
            cellStyle.setBorderRight(BorderStyle.THIN);
            cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());

        }

        // 获取sheet中的所有行，从第四行开始重新设置
        int rows = sheet.getPhysicalNumberOfRows();
        for (int i = 5; i < rows; i++) {
            Row row = sheet.getRow(i);
            row.setHeightInPoints(40); // 设置行高
            // 在这里可以执行所需的操作，例如重新设置单元格样式
            for (Cell cell : row) {
                // 重新设置单元格样式
                CellStyle cellStyle = workbook.createCellStyle();
                cellStyle.cloneStyleFrom(cell.getCellStyle());
                // 例如，重新设置边框样式
                setBorderStyle(cellStyle, workbook);
                cellStyle.setBorderRight(BorderStyle.THIN);
                cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
                cell.setCellStyle(cellStyle);


                CellStyle cellStyle1 = workbook.createCellStyle();
                cellStyle1.setFont(titleFont);
                cellStyle1.setAlignment(HorizontalAlignment.CENTER);
                cellStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
                setBorderStyle(cellStyle1, workbook); // 设置边框样式

                cell.setCellStyle(cellStyle1);

            }
        }

        return sheet;
    }

    //初步评审表
    public Sheet sheet2(Long projectId, Long resultId, Workbook workbook) throws IOException {
        Sheet sheet = workbook.createSheet("初步");
        //查询导出行所需要的数据信息
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>()
                .eq("project_id", projectId));
        //审计行标题
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                .eq("item_mode", "2")
        );
        //获取全部供应商
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId)
                .eq("decode_flag",1).eq("is_abandoned_bid",0));

        if (scoringMethodItems.size() > 0) {
            List<Long> scoringMethodItemIds = scoringMethodItems.stream().map(ScoringMethodItem::getScoringMethodItemId).collect(Collectors.toList());
            //查询因素信息
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                    .in("scoring_method_item_id", scoringMethodItemIds)
                    .eq("ent_method_id", scoringMethodUinfo.getEntMethodId()));
            Map<Long, List<ScoringMethodUitem>> listMap = scoringMethodUitems.stream()
                    .collect(Collectors.groupingBy(ScoringMethodUitem::getScoringMethodItemId));
            for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {
                scoringMethodItem.setUItems(listMap.get(scoringMethodItem.getScoringMethodItemId()));
            }
        } else {
            System.out.println("没有item信息");
        }
        //总计首页标题需要合并多少列（0，0，0，num）
        Integer num = calculateScoringMethodItemSizes(scoringMethodItems);
        System.out.println("合并多少列" + num);
        if (num == null || num < 0) {
            throw new IllegalArgumentException("Invalid number of columns to merge: " + num);
        }
        //通过项目id查询项目信息、专家信息及开标记录表
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日HH点mm分");
        String formattedDate = formatter.format(evaluationInfo.getEvaluationTime());
        // 指定要合并的列数
        // sheet.setColumnWidth(0, 60 * 256); // 第一列宽度
        ExcelStyleHelper styleHelper = new ExcelStyleHelper(workbook);
        CellStyle titleRowCellStyle = styleHelper.createTitleRowCellStyle();
        // 创建行并设置行高和内容
        for (int i = 0; i < (7 + busiBidderInfos.size()); i++) {
            Row row = sheet.createRow(i);
            row.setHeightInPoints(i < 5 ? 40 : 60); // 设置行高，假设前四行高度为20，其余为40
            for (int j = 0; j < num; j++) {
                Cell cell = row.createCell(j);
                sheet.setColumnWidth(j, 15 * 256);

                if (i == 0) {
                    cell.setCellStyle(styleHelper.createHeaderCellStyle());
                } else if (i < 4) {
                    cell.setCellStyle(styleHelper.createTitleCellStyle());
                } else if (i == 4) {
                    cell.setCellStyle(styleHelper.createTitleRightCellStyle());
                } else {
                    cell.setCellStyle(styleHelper.createContentCellStyle());
                }
                setBorderStyle(cell.getCellStyle(), workbook); // 设置单元格边框样式
            }
        }
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            firstRow = sheet.createRow(0);
        }
        Cell firstCell = firstRow.getCell(0);
        if (firstCell == null) {
            firstCell = firstRow.createCell(0);
        }
        firstCell.setCellValue("初步评审表");
        // 合并第一行的单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, num - 1));

        List<ScoringMethodUitem> combinedUItems = scoringMethodItems.stream()
                .map(ScoringMethodItem::getUItems)
                .filter(uItems -> uItems != null)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        //合并序列和供应商名称
        for (int i = 1; i <= 5; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            Cell secondCell = row.getCell(1);
            if (cell == null) {
                cell = row.createCell(1);
            }
            // 合并单元格
            // 根据行号设置不同的值
            switch (i) {
                case 1:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目名称：" + tenderProject.getProjectName());
                    break;
                case 2:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目编号：" + tenderProject.getProjectCode());
                    break;
                case 3:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("评标日期：" + formattedDate);
                    break;
                //case 4:
                    //sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                   // cell.setCellValue("注：通过“√”，未通过“×”");
                 //   break;
                case 4:
                    // 合并第五行的第一列
                    sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 0, 0));
                    cell.setCellValue("序号");
                    // 合并第五行的第二列
                    sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 1, 1));
                    secondCell.setCellValue("供应商名称");
                    for (int j = 0; j < scoringMethodItems.size(); j++) {

                        Map<Long, Map<String, Integer>> generateRowMap = generateResultMap(scoringMethodItems);
                        ScoringMethodItem scoringMethodItem = scoringMethodItems.get(j);
                        System.out.println(scoringMethodItem.getScoringMethodItemId());
                        Map<String, Integer> numMap = generateRowMap.get(scoringMethodItem.getScoringMethodItemId());
                        System.out.println(JSONObject.toJSONString(numMap));
                        sheet.addMergedRegion(new CellRangeAddress(4, 4, numMap.get("startNum"), numMap.get("entNum")));
                        System.out.println(scoringMethodItems.get(j).getItemName());
                        Cell cell1 = row.getCell(numMap.get("startNum"));
                        if (cell1 == null) {
                            cell1 = row.createCell(numMap.get("startNum"));
                        }
                        cell1.setCellValue(scoringMethodItems.get(j).getItemName());
                        //具体项目
                        scoringMethodItem.getUItems().get(0).getItemName();
                    }
                    break;
                case 5:
                    for (int colIndex = 0; colIndex < combinedUItems.size(); colIndex++) {
                        // 创建合并区域，起始行号是6（第七行），结束行号是7（第八行），列号是colIndex
                        sheet.addMergedRegion(new CellRangeAddress(5, 6, colIndex + 2, colIndex + 2));
                        Cell sixCell = row.getCell(colIndex + 2);
                        if (sixCell == null) {
                            sixCell = row.createCell(colIndex + 2);
                        }
                        row.setHeightInPoints(80); // 修改此处设置行高为50
                        sixCell.setCellValue(combinedUItems.get(colIndex).getItemName());
                        CellStyle wrapTextCellStyle = workbook.createCellStyle();
                        wrapTextCellStyle.cloneStyleFrom(styleHelper.createContentCellStyle()); // 复制已有样式
                        wrapTextCellStyle.setWrapText(true);
                        sixCell.setCellStyle(wrapTextCellStyle);
                    }
                    break;
            }

        }

        List<Map<String, Object>> data = new ArrayList<>();
        for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {

            Map<String, Object> resultMap = evalExpertEvaluationDetailService.getByEvalExpertEvaluationDetail(projectId, scoringMethodItem.getScoringMethodItemId(), resultId);
            List<Map<String, Object>> tableData = (ArrayList) resultMap.get("tableData");
            data.addAll(tableData);
        }

        Map<Object, List<Map<String, Object>>> gys = data.stream().collect(Collectors.groupingBy(item -> item.get("gys")));

        List<Map<String, Object>> result = new ArrayList<>();
        for (Object o : gys.keySet()) {
            // 可以拿到gys相同的对象集合
            Map<String, Object> newData = new HashMap<>();
            List<Map<String, Object>> maps = gys.get(o);
            for (Map<String, Object> map : maps) {
                // 利用map key唯一 覆盖的特性  添加所有的值进去
                newData.putAll(map);
            }
            result.add(newData);
        }

        System.out.println(JSON.toJSONString(result));

        Map<String, Map<String, Object>> entResult = new HashMap<>();
        for (Map<String, Object> entry : result) {
            String gysValue = entry.get("gys").toString();
            entResult.put(gysValue, entry);
        }

        // 使用Stream API将List转换为Map，其中键是bidderId，值是BusiBidderInfo对象
        Map<Long, BusiBidderInfo> bidderInfoMap = busiBidderInfos.stream()
                .collect(Collectors.toMap(BusiBidderInfo::getBidderId, bidderInfo -> bidderInfo));

        for (int i = 0; i < busiBidderInfos.size(); i++) {
            //每个供应商是一行
            Row row = sheet.getRow(7 + i);
            if (row == null) {
                row = sheet.createRow(7 + i);
            }
            //序号列
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            cell.setCellValue(i + 1);
            //供应商名称列
            Cell twoCell = row.getCell(1);
            if (twoCell == null) {
                twoCell = row.createCell(1);
            }
            BusiBidderInfo busiBidderInfo = busiBidderInfos.get(i);
            twoCell.setCellValue(busiBidderInfo.getBidderName());
            //所有供应商的因素值
            Map<String, Object> bindderInfoMap = entResult.get(busiBidderInfo.getBidderId() + "");
            System.out.println(JSONObject.toJSONString(bindderInfoMap));
            //循环因素值
            for (int colIndex = 0; colIndex < combinedUItems.size(); colIndex++) {
                Cell sixCell = row.getCell(colIndex + 2);
                if (sixCell == null) {
                    sixCell = row.createCell(colIndex + 2);
                }


                // 使用BigDecimal去除小数部分
                BigDecimal bigDecimalValue = new BigDecimal(bindderInfoMap.get(combinedUItems.get(colIndex).getEntMethodItemId()).toString());
                int intValue = bigDecimalValue.intValueExact(); // 这将抛出异常，如果stringValue不是整数
                sixCell.setCellValue(intValue == 1 ? "通过" : "不通过");
//                CellStyle wrapTextCellStyle = workbook.createCellStyle();
//                wrapTextCellStyle.cloneStyleFrom(styleHelper.createContentCellStyle()); // 复制已有样式
//                wrapTextCellStyle.setWrapText(true);
//                sixCell.setCellStyle(wrapTextCellStyle);
            }

        }

        // 计算新行的行号
        int newRowNumber = 7 + busiBidderInfos.size() + 2;
        // 创建新行
        Row newRow = sheet.createRow(newRowNumber);
        // 在第二列（索引为1）创建一个单元格
        Cell cell = newRow.createCell(1);
        // 设置单元格的值
        cell.setCellValue("评审专家签字：");
        // 自动调整行高以适应内容（注意：直接设置行高在某些情况下可能不准确，尤其是启用自动换行后）
        sheet.autoSizeColumn(0); // 根据内容自动调整列宽，但不直接调整行高
        return sheet;
    }

    //技术部分评审表
    public Sheet sheet3(Long projectId, Long resultId, Workbook workbook) throws IOException {
        Sheet sheet = workbook.createSheet("技术部分评审表");
        //查询导出行所需要的数据信息
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>()
                .eq("project_id", projectId));
        //审计行标题
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                //.eq("item_mode", "2")
                .eq("item_code", "jsbps")
        );
        //获取全部供应商
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));

        if (scoringMethodItems.size() > 0) {
            List<Long> scoringMethodItemIds = scoringMethodItems.stream().map(ScoringMethodItem::getScoringMethodItemId).collect(Collectors.toList());
            //查询因素信息
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                    .in("scoring_method_item_id", scoringMethodItemIds)
                    .eq("ent_method_id", scoringMethodUinfo.getEntMethodId()));

            Map<Long, List<ScoringMethodUitem>> listMap = scoringMethodUitems.stream()
                    .collect(Collectors.groupingBy(ScoringMethodUitem::getScoringMethodItemId));
            for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {
                scoringMethodItem.setUItems(listMap.get(scoringMethodItem.getScoringMethodItemId()));
            }
        } else {
            System.out.println("没有item信息");
        }
        //总计首页标题需要合并多少列（0，0，0，num）+1是指的合计列
        Integer num = calculateScoringMethodItemSizes(scoringMethodItems) + 1;
        System.out.println("合并多少列" + num);
        if (num == null || num < 0) {
            throw new IllegalArgumentException("Invalid number of columns to merge: " + num);
        }
        //通过项目id查询项目信息、专家信息及开标记录表
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日HH点mm分");
        String formattedDate = formatter.format(evaluationInfo.getEvaluationTime());
        // 指定要合并的列数
        // sheet.setColumnWidth(0, 60 * 256); // 第一列宽度
        ExcelStyleHelper styleHelper = new ExcelStyleHelper(workbook);
        CellStyle titleRowCellStyle = styleHelper.createTitleRowCellStyle();
        // 创建行并设置行高和内容
        for (int i = 0; i < (8 + busiBidderInfos.size()); i++) {
            Row row = sheet.createRow(i);
            row.setHeightInPoints(i < 5 ? 40 : 60); // 设置行高，假设前四行高度为20，其余为40
            for (int j = 0; j < num; j++) {
                Cell cell = row.createCell(j);
                sheet.setColumnWidth(j, 15 * 256);

                if (i == 0) {
                    cell.setCellStyle(styleHelper.createHeaderCellStyle());
                } else if (i < 4) {
                    cell.setCellStyle(styleHelper.createTitleCellStyle());
                } else if (i == 4) {
                    cell.setCellStyle(styleHelper.createTitleRightCellStyle());
                } else {
                    cell.setCellStyle(styleHelper.createContentCellStyle());
                }
                setBorderStyle(cell.getCellStyle(), workbook); // 设置单元格边框样式
            }
        }
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            firstRow = sheet.createRow(0);
        }
        Cell firstCell = firstRow.getCell(0);
        if (firstCell == null) {
            firstCell = firstRow.createCell(0);
        }
        firstCell.setCellValue("技术部分评审表");
        // 合并第一行的单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, num - 1));

        List<ScoringMethodUitem> combinedUItems = scoringMethodItems.stream()
                .map(ScoringMethodItem::getUItems)
                .filter(uItems -> uItems != null)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        ScoringMethodUitem scoringMethodUitem = new ScoringMethodUitem();
        scoringMethodUitem.setEntMethodItemId(1l);
        scoringMethodUitem.setItemName("合计");
        combinedUItems.add(scoringMethodUitem);

        //合并序列和供应商名称
        for (int i = 1; i <= 6; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            Cell secondCell = row.getCell(1);
            if (cell == null) {
                cell = row.createCell(1);
            }
            // 合并单元格
            // 根据行号设置不同的值
            switch (i) {
                case 1:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目名称：" + tenderProject.getProjectName());
                    break;
                case 2:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目编号：" + tenderProject.getProjectCode());
                    break;
                case 3:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("评标日期：" + formattedDate);
                    break;
                case 4:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("注：通过“√”，未通过“×”");
                    break;
                case 5:
                    // 合并第五行的第一列
                    sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 0, 0));
                    cell.setCellValue("序号");
                    // 合并第五行的第二列
                    sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 1, 1));
                    secondCell.setCellValue("供应商名称");
                    for (int j = 0; j < scoringMethodItems.size(); j++) {

                        Map<Long, Map<String, Integer>> generateRowMap = generateResultMap(scoringMethodItems);
                        ScoringMethodItem scoringMethodItem = scoringMethodItems.get(j);
                        System.out.println(scoringMethodItem.getScoringMethodItemId());
                        Map<String, Integer> numMap = generateRowMap.get(scoringMethodItem.getScoringMethodItemId());
                        System.out.println(JSONObject.toJSONString(numMap));
                        if (scoringMethodItems.get(j).getItemCode() == "") {
                            sheet.addMergedRegion(new CellRangeAddress(5, 5, numMap.get("startNum"), numMap.get("entNum") + 1));

                        } else {
                            sheet.addMergedRegion(new CellRangeAddress(5, 5, numMap.get("startNum"), numMap.get("entNum") + 1));

                        }
                        System.out.println(scoringMethodItems.get(j).getItemName());
                        Cell cell1 = row.getCell(numMap.get("startNum"));
                        if (cell1 == null) {
                            cell1 = row.createCell(numMap.get("startNum"));
                        }
                        cell1.setCellValue(scoringMethodItems.get(j).getItemName());
                        //具体项目
                        scoringMethodItem.getUItems().get(0).getItemName();
                    }
                    break;
                case 6:
                    for (int colIndex = 0; colIndex < combinedUItems.size(); colIndex++) {
                        // 创建合并区域，起始行号是6（第七行），结束行号是7（第八行），列号是colIndex
                        sheet.addMergedRegion(new CellRangeAddress(6, 7, colIndex + 2, colIndex + 2));
                        Cell sixCell = row.getCell(colIndex + 2);
                        if (sixCell == null) {
                            sixCell = row.createCell(colIndex + 2);
                        }
                        row.setHeightInPoints(80); // 修改此处设置行高为50
                        sixCell.setCellValue(combinedUItems.get(colIndex).getItemName());
                        CellStyle wrapTextCellStyle = workbook.createCellStyle();
                        wrapTextCellStyle.cloneStyleFrom(styleHelper.createContentCellStyle()); // 复制已有样式
                        wrapTextCellStyle.setWrapText(true);
                        sixCell.setCellStyle(wrapTextCellStyle);
                    }
                    break;
            }

        }

        List<Map<String, Object>> data = new ArrayList<>();
        for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {

            Map<String, Object> resultMap = evalExpertEvaluationDetailService.getByEvalExpertEvaluationDetail(projectId, scoringMethodItem.getScoringMethodItemId(), resultId);
            List<Map<String, Object>> tableData = (ArrayList) resultMap.get("tableData");
            data.addAll(tableData);
        }

        Map<Object, List<Map<String, Object>>> gys = data.stream().collect(Collectors.groupingBy(item -> item.get("gys")));

        List<Map<String, Object>> result = new ArrayList<>();
        for (Object o : gys.keySet()) {
            // 可以拿到gys相同的对象集合
            Map<String, Object> newData = new HashMap<>();
            List<Map<String, Object>> maps = gys.get(o);
            for (Map<String, Object> map : maps) {
                // 利用map key唯一 覆盖的特性  添加所有的值进去
                newData.putAll(map);
            }
            result.add(newData);
        }

        System.out.println(JSON.toJSONString(result));

        Map<String, Map<String, Object>> entResult = new HashMap<>();
        for (Map<String, Object> entry : result) {
            String gysValue = entry.get("gys").toString();
            entResult.put(gysValue, entry);
        }

        // 使用Stream API将List转换为Map，其中键是bidderId，值是BusiBidderInfo对象
        Map<Long, BusiBidderInfo> bidderInfoMap = busiBidderInfos.stream()
                .collect(Collectors.toMap(BusiBidderInfo::getBidderId, bidderInfo -> bidderInfo));

        for (int i = 0; i < busiBidderInfos.size(); i++) {
            //每个供应商是一行
            Row row = sheet.getRow(8 + i);
            if (row == null) {
                row = sheet.createRow(8 + i);
            }
            //序号列
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            cell.setCellValue(i + 1);
            //供应商名称列
            Cell twoCell = row.getCell(1);
            if (twoCell == null) {
                twoCell = row.createCell(1);
            }
            BusiBidderInfo busiBidderInfo = busiBidderInfos.get(i);
            twoCell.setCellValue(busiBidderInfo.getBidderName());
            //所有供应商的因素值
            Map<String, Object> bindderInfoMap = entResult.get(busiBidderInfo.getBidderId() + "");
            System.out.println(JSONObject.toJSONString(bindderInfoMap));

            //循环因素值
            for (int colIndex = 0; colIndex < combinedUItems.size(); colIndex++) {
                Cell sixCell = row.getCell(colIndex + 2);
                if (sixCell == null) {
                    sixCell = row.createCell(colIndex + 2);
                }
                System.out.println(combinedUItems.get(colIndex).getEntMethodItemId());
                System.out.println(bindderInfoMap.get(combinedUItems.get(colIndex).getEntMethodItemId().toString()));
                sixCell.setCellValue(bindderInfoMap.get(combinedUItems.get(colIndex).getEntMethodItemId().toString()).toString());
//                CellStyle wrapTextCellStyle = workbook.createCellStyle();
//                wrapTextCellStyle.cloneStyleFrom(styleHelper.createContentCellStyle()); // 复制已有样式
//                wrapTextCellStyle.setWrapText(true);
//                sixCell.setCellStyle(wrapTextCellStyle);
            }

        }

        // 计算新行的行号
        int newRowNumber = 8 + busiBidderInfos.size() + 2;
        // 创建新行
        Row newRow = sheet.createRow(newRowNumber);
        // 在第二列（索引为1）创建一个单元格
        Cell cell = newRow.createCell(1);
        // 设置单元格的值
        cell.setCellValue("评审专家签字：");
        return sheet;
    }

    //技术分汇总表
    public Sheet sheet4(Long projectId, Long resultId, Workbook workbook) throws IOException {
        Sheet sheet = workbook.createSheet("技术分汇总表");
        //查询导出行所需要的数据信息
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>()
                .eq("project_id", projectId));
        //审计行标题
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                //.eq("item_mode", "2")
                .eq("item_code", "jsbps")
        );

        Map<String, Object> resultMap = evalExpertEvaluationDetailService.getEalExpertEvaluationDetailToGroupLeader(projectId, scoringMethodItems.get(0).getScoringMethodItemId());
        List<Map<String, Object>> tableData = (ArrayList) resultMap.get("tableData");
        List<Map<String, Object>> tableColumns = (ArrayList) resultMap.get("tableColumns");
        System.out.println(JSONObject.toJSONString(tableColumns));
        System.out.println(JSONObject.toJSONString(tableData));
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId)
        .eq("decode_flag",1).eq("is_abandoned_bid",0));

        //获取项目所有专家
        BusiExtractExpertResult busiExtractExpertResult = new BusiExtractExpertResult();
        busiExtractExpertResult.setProjectId(projectId);
        List<BusiExtractExpertResult> expertList = extractExpertResultService.getZhuanJiaByProjectId(busiExtractExpertResult);


        //总计首页标题需要合并多少列（0，0，0，num）+1是指的合计列
        // Integer num = calculateScoringMethodItemSizes(scoringMethodItems)+1;
        Integer num = expertList.size() + 3;
        System.out.println("合并多少列" + num);
        if (num == null || num < 0) {
            throw new IllegalArgumentException("Invalid number of columns to merge: " + num);
        }
        //通过项目id查询项目信息、专家信息及开标记录表
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日HH点mm分");
        String formattedDate = formatter.format(evaluationInfo.getEvaluationTime());
        // 指定要合并的列数
        // sheet.setColumnWidth(0, 60 * 256); // 第一列宽度
        ExcelStyleHelper styleHelper = new ExcelStyleHelper(workbook);
        CellStyle titleRowCellStyle = styleHelper.createTitleRowCellStyle();
        // 创建行并设置行高和内容
        for (int i = 0; i < (5 + busiBidderInfos.size()); i++) {
            Row row = sheet.createRow(i);
            row.setHeightInPoints(i < 5 ? 40 : 60); // 设置行高，假设前四行高度为20，其余为40
            for (int j = 0; j < num; j++) {
                Cell cell = row.createCell(j);
                sheet.setColumnWidth(j, 15 * 256);
                if (i == 0) {
                    cell.setCellStyle(styleHelper.createHeaderCellStyle());
                } else if (i < 4) {
                    cell.setCellStyle(styleHelper.createTitleCellStyle());
                }
//                else if (i == 4) {
//                    cell.setCellStyle(styleHelper.createTitleRightCellStyle());
//                }
                else {
                    cell.setCellStyle(styleHelper.createContentCellStyle());
                }
                setBorderStyle(cell.getCellStyle(), workbook); // 设置单元格边框样式
            }
        }
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            firstRow = sheet.createRow(0);
        }
        Cell firstCell = firstRow.getCell(0);
        if (firstCell == null) {
            firstCell = firstRow.createCell(0);
        }
        firstCell.setCellValue("技术分汇总表");
        // 合并第一行的单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, num - 1));

        //合并序列和供应商名称
        for (int i = 1; i <= 6; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            Cell secondCell = row.getCell(1);
            if (cell == null) {
                cell = row.createCell(1);
            }
            // 合并单元格
            // 根据行号设置不同的值
            switch (i) {
                case 1:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目名称：" + tenderProject.getProjectName());
                    break;
                case 2:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目编号：" + tenderProject.getProjectCode());
                    break;
                case 3:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("评标日期：" + formattedDate);
                    break;
                case 4:
                    // 合并第五行的第一列
                    //sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 0, 0));
                    cell.setCellValue("序号");
                    // 合并第五行的第二列
                    //sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 1, 1));
                    secondCell.setCellValue("专家名称");
                    for (int j = 0; j < expertList.size(); j++) {
                        Cell jCell = row.getCell(j + 2);
                        if (jCell == null) {
                            jCell = row.createCell(j + 2);
                        }
                        //BusiExtractExpertResult busiExtractExpertResult1 = JSONObject.parseObject(JSONObject.toJSONString(tableColumns.get(j)), BusiExtractExpertResult.class);

                        jCell.setCellValue(expertList.get(j).getExpertName());
                    }
                    Cell jCell = row.getCell(tableColumns.size() + 2);
                    if (jCell == null) {
                        jCell = row.createCell(tableColumns.size() + 2);
                    }
                    jCell.setCellValue("平均值");
                    break;
            }

        }

        for (int i = 0; i < busiBidderInfos.size(); i++) {
            //每个供应商是一行
            Row row = sheet.getRow(5 + i);
            if (row == null) {
                row = sheet.createRow(5 + i);
            }
            //序号列
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            cell.setCellValue(i + 1);
            //供应商名称列
            Cell twoCell = row.getCell(1);
            if (twoCell == null) {
                twoCell = row.createCell(1);
            }
            BusiBidderInfo busiBidderInfo = busiBidderInfos.get(i);
            twoCell.setCellValue(busiBidderInfo.getBidderName());


            Map<String, Map<String, Object>> entResult = new HashMap<>();
            for (Map<String, Object> entry : tableData) {
                String gysValue = entry.get("gys").toString();
                entResult.put(gysValue, entry);
            }
            Map<String, Object> bindderInfoMap = entResult.get(busiBidderInfo.getBidderId() + "");
            System.out.println(JSONObject.toJSONString(bindderInfoMap));
            System.out.println(JSONObject.toJSONString(expertList));
            for (int j = 0; j < expertList.size(); j++) {
                Cell sixCell = row.getCell(j + 2);
                if (sixCell == null) {
                    sixCell = row.createCell(j + 2);
                }
                Long string = Long.parseLong(expertList.get(j).getResultId().toString());
                if (expertList.get(j).getResultId() == 1l) {
                    // sixCell.setCellValue(bindderInfoMap.get(expertList.get(j).getResultId().toString()).toString());

                    try {
                        // 尝试将字符串转换为数字
                        double numericValue = Double.parseDouble(bindderInfoMap.get(expertList.get(j).getResultId().toString()).toString());
                        sixCell.setCellValue(numericValue);
                    } catch (NumberFormatException e) {
                        // 如果转换失败，可以设置错误信息或者默认值
                        sixCell.setCellValue((double) 0);
                    }
                } else {
                    try {
                        // 尝试将字符串转换为数字
                        double numericValue = Double.parseDouble(bindderInfoMap.get(string).toString());
                        sixCell.setCellValue(numericValue);
                    } catch (NumberFormatException e) {
                        // 如果转换失败，可以设置错误信息或者默认值
                        sixCell.setCellValue((double) 0);
                    }


                    //sixCell.setCellValue(bindderInfoMap.get(string).toString());
                }
            }
            Cell sixCell = row.getCell(expertList.size() + 2);
            if (sixCell == null) {
                sixCell = row.createCell(expertList.size() + 2);
            }
            getStartAndEndCoordinates(expertList, 3);
           //sixCell.setCellFormula("AVERAGE(C6:D6)");
            // 动态生成AVERAGE函数的单元格范围
            StringBuilder formulaBuilder = new StringBuilder("ROUND(AVERAGE(");
            char startColumn = (char) ('B' + 1); // 从第三列开始，即'C'
            char endColumn = (char) (startColumn + expertList.size() - 1); // 计算结束列
            formulaBuilder.append(startColumn).append(6 + i).append(':').append(endColumn).append(6 + i).append("), 2)");
            String dynamicFormula = formulaBuilder.toString();
            // 设置动态生成的AVERAGE公式
            sixCell.setCellFormula(dynamicFormula);
        }

        // 计算新行的行号
        int newRowNumber = 4 + busiBidderInfos.size() + 2;
        // 创建新行
        Row newRow = sheet.createRow(newRowNumber);
        // 在第二列（索引为1）创建一个单元格
        Cell cell = newRow.createCell(1);
        // 设置单元格的值
        cell.setCellValue("评审专家签字：");
        // 自动调整行高以适应内容（注意：直接设置行高在某些情况下可能不准确，尤其是启用自动换行后）
        sheet.autoSizeColumn(0); // 根据内容自动调整列宽，但不直接调整行高
        return sheet;
    }

    //商务部分评审表(综合)
    public Sheet sheet5(Long projectId, Long resultId, Workbook workbook) throws IOException {
        Sheet sheet = workbook.createSheet("商务部分评审表");
        //查询导出行所需要的数据信息
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>()
                .eq("project_id", projectId));
        //审计行标题
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                //.eq("item_mode", "2")
                .eq("item_code", "swbps")
        );
        //获取全部供应商
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId));

        if (scoringMethodItems.size() > 0) {
            List<Long> scoringMethodItemIds = scoringMethodItems.stream().map(ScoringMethodItem::getScoringMethodItemId).collect(Collectors.toList());
            //查询因素信息
            List<ScoringMethodUitem> scoringMethodUitems = scoringMethodUitemService.list(new QueryWrapper<ScoringMethodUitem>()
                    .in("scoring_method_item_id", scoringMethodItemIds)
                    .eq("ent_method_id", scoringMethodUinfo.getEntMethodId()));

            Map<Long, List<ScoringMethodUitem>> listMap = scoringMethodUitems.stream()
                    .collect(Collectors.groupingBy(ScoringMethodUitem::getScoringMethodItemId));
            for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {
                scoringMethodItem.setUItems(listMap.get(scoringMethodItem.getScoringMethodItemId()));
            }
        } else {
            System.out.println("没有item信息");
        }
        //总计首页标题需要合并多少列（0，0，0，num）+1是指的合计列
        Integer num = calculateScoringMethodItemSizes(scoringMethodItems) + 1;
        System.out.println("合并多少列" + num);
        if (num == null || num < 0) {
            throw new IllegalArgumentException("Invalid number of columns to merge: " + num);
        }
        //通过项目id查询项目信息、专家信息及开标记录表
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日HH点mm分");
        String formattedDate = formatter.format(evaluationInfo.getEvaluationTime());
        // 指定要合并的列数
        // sheet.setColumnWidth(0, 60 * 256); // 第一列宽度
        ExcelStyleHelper styleHelper = new ExcelStyleHelper(workbook);
        CellStyle titleRowCellStyle = styleHelper.createTitleRowCellStyle();
        // 创建行并设置行高和内容
        for (int i = 0; i < (8 + busiBidderInfos.size()); i++) {
            Row row = sheet.createRow(i);
            row.setHeightInPoints(i < 5 ? 20 : 40); // 设置行高，假设前四行高度为20，其余为40
            for (int j = 0; j < num; j++) {
                Cell cell = row.createCell(j);
                sheet.setColumnWidth(j, 15 * 256);

                if (i == 0) {
                    cell.setCellStyle(styleHelper.createHeaderCellStyle());
                } else if (i < 4) {
                    cell.setCellStyle(styleHelper.createTitleCellStyle());
                } else if (i == 4) {
                    cell.setCellStyle(styleHelper.createTitleRightCellStyle());
                } else {
                    cell.setCellStyle(styleHelper.createContentCellStyle());
                }
                setBorderStyle(cell.getCellStyle(), workbook); // 设置单元格边框样式
            }
        }
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            firstRow = sheet.createRow(0);
        }
        Cell firstCell = firstRow.getCell(0);
        if (firstCell == null) {
            firstCell = firstRow.createCell(0);
        }
        firstCell.setCellValue("商务部分评审表");
        // 合并第一行的单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, num - 1));

        List<ScoringMethodUitem> combinedUItems = scoringMethodItems.stream()
                .map(ScoringMethodItem::getUItems)
                .filter(uItems -> uItems != null)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        ScoringMethodUitem scoringMethodUitem = new ScoringMethodUitem();
        scoringMethodUitem.setEntMethodItemId(1l);
        scoringMethodUitem.setItemName("合计");
        combinedUItems.add(scoringMethodUitem);

        //合并序列和供应商名称
        for (int i = 1; i <= 6; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            Cell secondCell = row.getCell(1);
            if (cell == null) {
                cell = row.createCell(1);
            }
            // 合并单元格
            // 根据行号设置不同的值
            switch (i) {
                case 1:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目名称：" + tenderProject.getProjectName());
                    break;
                case 2:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目编号：" + tenderProject.getProjectCode());
                    break;
                case 3:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("评标日期：" + formattedDate);
                    break;
                case 4:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("注：通过“√”，未通过“×”");
                    break;
                case 5:
                    // 合并第五行的第一列
                    sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 0, 0));
                    cell.setCellValue("序号");
                    // 合并第五行的第二列
                    sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 1, 1));
                    secondCell.setCellValue("供应商名称");
                    for (int j = 0; j < scoringMethodItems.size(); j++) {

                        Map<Long, Map<String, Integer>> generateRowMap = generateResultMap(scoringMethodItems);
                        ScoringMethodItem scoringMethodItem = scoringMethodItems.get(j);
                        System.out.println(scoringMethodItem.getScoringMethodItemId());
                        Map<String, Integer> numMap = generateRowMap.get(scoringMethodItem.getScoringMethodItemId());
                        System.out.println(JSONObject.toJSONString(numMap));
                        if (scoringMethodItems.get(j).getItemCode() == "") {
                            sheet.addMergedRegion(new CellRangeAddress(5, 5, numMap.get("startNum"), numMap.get("entNum") + 1));

                        } else {
                            sheet.addMergedRegion(new CellRangeAddress(5, 5, numMap.get("startNum"), numMap.get("entNum") + 1));

                        }
                        System.out.println(scoringMethodItems.get(j).getItemName());
                        Cell cell1 = row.getCell(numMap.get("startNum"));
                        if (cell1 == null) {
                            cell1 = row.createCell(numMap.get("startNum"));
                        }
                        cell1.setCellValue(scoringMethodItems.get(j).getItemName());
                        //具体项目
                        scoringMethodItem.getUItems().get(0).getItemName();
                    }
                    break;
                case 6:
                    for (int colIndex = 0; colIndex < combinedUItems.size(); colIndex++) {
                        // 创建合并区域，起始行号是6（第七行），结束行号是7（第八行），列号是colIndex
                        sheet.addMergedRegion(new CellRangeAddress(6, 7, colIndex + 2, colIndex + 2));
                        Cell sixCell = row.getCell(colIndex + 2);
                        if (sixCell == null) {
                            sixCell = row.createCell(colIndex + 2);
                        }
                        row.setHeightInPoints(80); // 修改此处设置行高为50
                        sixCell.setCellValue(combinedUItems.get(colIndex).getItemName());
                        CellStyle wrapTextCellStyle = workbook.createCellStyle();
                        wrapTextCellStyle.cloneStyleFrom(styleHelper.createContentCellStyle()); // 复制已有样式
                        wrapTextCellStyle.setWrapText(true);
                        sixCell.setCellStyle(wrapTextCellStyle);
                    }
                    break;
            }

        }

        List<Map<String, Object>> data = new ArrayList<>();
        for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {

            Map<String, Object> resultMap = evalExpertEvaluationDetailService.getByEvalExpertEvaluationDetail(projectId, scoringMethodItem.getScoringMethodItemId(), resultId);
            List<Map<String, Object>> tableData = (ArrayList) resultMap.get("tableData");
            data.addAll(tableData);
        }

        Map<Object, List<Map<String, Object>>> gys = data.stream().collect(Collectors.groupingBy(item -> item.get("gys")));

        List<Map<String, Object>> result = new ArrayList<>();
        for (Object o : gys.keySet()) {
            // 可以拿到gys相同的对象集合
            Map<String, Object> newData = new HashMap<>();
            List<Map<String, Object>> maps = gys.get(o);
            for (Map<String, Object> map : maps) {
                // 利用map key唯一 覆盖的特性  添加所有的值进去
                newData.putAll(map);
            }
            result.add(newData);
        }

        System.out.println(JSON.toJSONString(result));

        Map<String, Map<String, Object>> entResult = new HashMap<>();
        for (Map<String, Object> entry : result) {
            String gysValue = entry.get("gys").toString();
            entry.remove("gys");
            entResult.put(gysValue, entry);
        }

        // 使用Stream API将List转换为Map，其中键是bidderId，值是BusiBidderInfo对象
        Map<Long, BusiBidderInfo> bidderInfoMap = busiBidderInfos.stream()
                .collect(Collectors.toMap(BusiBidderInfo::getBidderId, bidderInfo -> bidderInfo));

        for (int i = 0; i < busiBidderInfos.size(); i++) {
            //每个供应商是一行
            Row row = sheet.getRow(8 + i);
            if (row == null) {
                row = sheet.createRow(8 + i);
            }
            //序号列
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            cell.setCellValue(i + 1);
            //供应商名称列
            Cell twoCell = row.getCell(1);
            if (twoCell == null) {
                twoCell = row.createCell(1);
            }
            BusiBidderInfo busiBidderInfo = busiBidderInfos.get(i);
            twoCell.setCellValue(busiBidderInfo.getBidderName());
            //所有供应商的因素值
            Map<String, Object> bindderInfoMap = entResult.get(busiBidderInfo.getBidderId() + "");
            System.out.println(JSONObject.toJSONString(bindderInfoMap));

            //循环因素值
            for (int colIndex = 0; colIndex < combinedUItems.size(); colIndex++) {
                Cell sixCell = row.getCell(colIndex + 2);
                if (sixCell == null) {
                    sixCell = row.createCell(colIndex + 2);
                }
                System.out.println(combinedUItems.get(colIndex).getEntMethodItemId());
                System.out.println(bindderInfoMap.get(combinedUItems.get(colIndex).getEntMethodItemId()));
                if (1==combinedUItems.get(colIndex).getEntMethodItemId()){
                    //设置colIndex + 2到combinedUItems.size-1+2求和
                    double sum = bindderInfoMap.values().stream()
                            // 将Object类型的值转换为Double类型（这里假设值都能正确转换为数值类型）
                            .mapToDouble(obj -> Double.parseDouble(obj.toString()))
                            .sum();
                    sixCell.setCellValue(sum);
                    continue;
                }
                sixCell.setCellValue(bindderInfoMap.get(combinedUItems.get(colIndex).getEntMethodItemId()).toString());

//                CellStyle wrapTextCellStyle = workbook.createCellStyle();
//                wrapTextCellStyle.cloneStyleFrom(styleHelper.createContentCellStyle()); // 复制已有样式
//                wrapTextCellStyle.setWrapText(true);
//                sixCell.setCellStyle(wrapTextCellStyle);
            }

        }
        // 计算新行的行号
        int newRowNumber = 8 + busiBidderInfos.size() + 2;
        // 创建新行
        Row newRow = sheet.createRow(newRowNumber);
        // 在第二列（索引为1）创建一个单元格
        Cell cell = newRow.createCell(1);
        // 设置单元格的值
        cell.setCellValue("评审专家签字：");
        return sheet;
    }

    //商务分汇总表
    public Sheet sheet6(Long projectId, Long resultId, Workbook workbook) throws IOException {
        Sheet sheet = workbook.createSheet("商务分汇总表");
        //查询导出行所需要的数据信息
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>()
                .eq("project_id", projectId));
        //审计行标题
        if (scoringMethodUinfo == null) {
            throw new RuntimeException("未查询到项目 ID 为 " + projectId + " 的评分方法配置");
        }
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                //.eq("item_mode", "2")
                .eq("item_code", "swbps")
        );
        if (scoringMethodItems == null || scoringMethodItems.isEmpty()) {
            throw new RuntimeException("未查询到商务分评分项配置");
        }

        EvalProjectEvaluationInfo evaluationInfoServiceOne = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
        if (evaluationInfoServiceOne == null) {
            throw new RuntimeException("未查询到项目 ID 为 " + projectId + " 的评审信息");
        }

        EvalProjectEvaluationProcess evaluationProcess = iEvalProjectEvaluationProcessService.getOne(new QueryWrapper<EvalProjectEvaluationProcess>()
                .eq("project_evaluation_id", evaluationInfoServiceOne.getProjectEvaluationId())
                .eq("scoring_method_item_id", scoringMethodItems.get(0).getScoringMethodItemId()));

        if (evaluationProcess == null || StringUtils.isBlank(evaluationProcess.getEvaluationResult())) {
            throw new RuntimeException("未查询到商务分评审结果");
        }
// 解析评审结果（处理JSON解析异常）
        List<EvaluationResultVo> evaluationResultVoList;
        try {
            evaluationResultVoList = JSON.parseObject(
                    evaluationProcess.getEvaluationResult(),
                    new TypeReference<List<EvaluationResultVo>>() {}
            );
        } catch (Exception e) {
            throw new RuntimeException("评审结果JSON解析失败：" + e.getMessage(), e);
        }
//        if (evaluationResultVoList!=null){
//            Map<Long, String> map = evaluationResultVoList.stream()
//                    .collect(Collectors.toMap(
//                            EvaluationResultVo::getBidder,
//                            EvaluationResultVo::getResult
//                    ));
//           // itemMap.put(scoringMethodItem.getScoringMethodItemId(),map);
//        }
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId)
                .eq("decode_flag",1).eq("is_abandoned_bid",0));
        if (busiBidderInfos.isEmpty()) {
            throw new RuntimeException("未查询到有效投标人信息");
        }
        // 查询专家列表
        BusiExtractExpertResult queryExpert = new BusiExtractExpertResult();
        queryExpert.setProjectId(projectId);
        List<BusiExtractExpertResult> expertList = extractExpertResultService.getZhuanJiaByProjectId(queryExpert);
        if (expertList.isEmpty()) {
            throw new RuntimeException("未查询到项目专家列表");
        }

        Map<String, Object> resultMap = evalExpertEvaluationDetailService.getEalExpertEvaluationDetailToGroupLeader(projectId, scoringMethodItems.get(0).getScoringMethodItemId());
        List<Map<String, Object>> tableData = (ArrayList) resultMap.get("tableData");
        List<Map<String, Object>> tableColumns = (ArrayList) resultMap.get("tableColumns");
        System.out.println(JSONObject.toJSONString(tableColumns));
        System.out.println(JSONObject.toJSONString(tableData));

        //总计首页标题需要合并多少列（0，0，0，num）+1是指的合计列
        // Integer num = calculateScoringMethodItemSizes(scoringMethodItems)+1;
        Integer num = expertList.size() + 3;
        System.out.println("合并多少列" + num);
        if (num == null || num < 0) {
            throw new IllegalArgumentException("Invalid number of columns to merge: " + num);
        }
        //通过项目id查询项目信息、专家信息及开标记录表
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
      //  EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日HH点mm分");
        String formattedDate = formatter.format(evaluationInfoServiceOne.getEvaluationTime());
        // 指定要合并的列数
        // sheet.setColumnWidth(0, 60 * 256); // 第一列宽度
        ExcelStyleHelper styleHelper = new ExcelStyleHelper(workbook);
        CellStyle titleRowCellStyle = styleHelper.createTitleRowCellStyle();
        // 创建行并设置行高和内容
        for (int i = 0; i < (5 + busiBidderInfos.size()); i++) {
            Row row = sheet.createRow(i);
            row.setHeightInPoints(i < 5 ? 40 : 60); // 设置行高，假设前四行高度为20，其余为40
            for (int j = 0; j < num; j++) {
                Cell cell = row.createCell(j);
                sheet.setColumnWidth(j, 15 * 256);
                if (i == 0) {
                    cell.setCellStyle(styleHelper.createHeaderCellStyle());
                } else if (i < 4) {
                    cell.setCellStyle(styleHelper.createTitleCellStyle());
                }
//                else if (i == 4) {
//                    cell.setCellStyle(styleHelper.createTitleRightCellStyle());
//                }
                else {
                    cell.setCellStyle(styleHelper.createContentCellStyle());
                }
                setBorderStyle(cell.getCellStyle(), workbook); // 设置单元格边框样式
            }
        }
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            firstRow = sheet.createRow(0);
        }
        Cell firstCell = firstRow.getCell(0);
        if (firstCell == null) {
            firstCell = firstRow.createCell(0);
        }
        firstCell.setCellValue("商务分汇总表");
        // 合并第一行的单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, num - 1));

        //合并序列和供应商名称
        for (int i = 1; i <= 6; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            Cell secondCell = row.getCell(1);
            if (cell == null) {
                cell = row.createCell(1);
            }
            // 合并单元格
            // 根据行号设置不同的值
            switch (i) {
                case 1:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目名称：" + tenderProject.getProjectName());
                    break;
                case 2:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目编号：" + tenderProject.getProjectCode());
                    break;
                case 3:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("评标日期：" + formattedDate);
                    break;
                case 4:
                    // 合并第五行的第一列
                    //sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 0, 0));
                    cell.setCellValue("序号");
                    // 合并第五行的第二列
                    //sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 1, 1));
                    secondCell.setCellValue("专家名称");
                    for (int j = 0; j < expertList.size(); j++) {
                        Cell jCell = row.getCell(j + 2);
                        if (jCell == null) {
                            jCell = row.createCell(j + 2);
                        }
                        //BusiExtractExpertResult busiExtractExpertResult1 = JSONObject.parseObject(JSONObject.toJSONString(tableColumns.get(j)), BusiExtractExpertResult.class);

                        jCell.setCellValue(expertList.get(j).getExpertName());
                    }
                    Cell jCell = row.getCell(tableColumns.size() + 2);
                    if (jCell == null) {
                        jCell = row.createCell(tableColumns.size() + 2);
                    }
                    jCell.setCellValue("评审结果");
                    break;
            }

        }
        // 构建供应商ID到评审结果的映射，提高查询效率
        Map<String, String> bidderResultMap = evaluationResultVoList.stream()
             //   .filter(vo -> vo.getBidder() != null)
                .collect(Collectors.toMap(
                        vo -> String.valueOf(vo.getBidder()),
                        EvaluationResultVo::getResult,
                        (existing, replacement) -> replacement  // 处理重复的bidder，保留最新值
                ));
        BusiExtractExpertResult busiExtractExpertResult =new BusiExtractExpertResult();
        busiExtractExpertResult.setResultId(1l);
        expertList.add(busiExtractExpertResult);
        for (int i = 0; i < busiBidderInfos.size(); i++) {
            //每个供应商是一行
            Row row = sheet.getRow(5 + i);
            if (row == null) {
                row = sheet.createRow(5 + i);
            }
            //序号列
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            cell.setCellValue(i + 1);
            //供应商名称列
            Cell twoCell = row.getCell(1);
            if (twoCell == null) {
                twoCell = row.createCell(1);
            }
            BusiBidderInfo busiBidderInfo = busiBidderInfos.get(i);
            twoCell.setCellValue(busiBidderInfo.getBidderName());


            Map<String, Map<String, Object>> entResult = new HashMap<>();
            for (Map<String, Object> entry : tableData) {
                String gysValue = entry.get("gys").toString();
                if (evaluationResultVoList == null) {
                    throw new RuntimeException("节点结束值为空");
                }
                if (evaluationResultVoList.size()>0){
                    Optional<EvaluationResultVo> first = evaluationResultVoList.stream()
                            // 过滤条件：bidder不为null，且转换为字符串后与gysValue相等
                            .filter(vo -> String.valueOf(vo.getBidder()).equals(gysValue))
                            // 获取第一个匹配的元素（若有多个，只返回第一个）
                            .findFirst();
                   entry.put("1",first.get().getResult());
                }
                entResult.put(gysValue, entry);
            }
            Map<String, Object> bindderInfoMap = entResult.get(busiBidderInfo.getBidderId() + "");
            System.out.println(JSONObject.toJSONString(bindderInfoMap));
            System.out.println(JSONObject.toJSONString(expertList));

            for (int j = 0; j < expertList.size(); j++) {
                Cell sixCell = row.getCell(j + 2);
                if (sixCell == null) {
                    sixCell = row.createCell(j + 2);
                }
                Long string = Long.parseLong(expertList.get(j).getResultId().toString());
                System.out.println("AAA" + string);
                if (expertList.get(j).getResultId() == 1l) {
                    // sixCell.setCellValue(bindderInfoMap.get(expertList.get(j).getResultId().toString()).toString());
                    try {
                        // 尝试将字符串转换为数字
                        double numericValue = Double.parseDouble(bindderInfoMap.get(expertList.get(j).getResultId().toString()).toString());
                        sixCell.setCellValue(numericValue);
                    } catch (NumberFormatException e) {
                        // 如果转换失败，可以设置错误信息或者默认值
                        sixCell.setCellValue((double) 0);
                    }
                } else {
                    try {
                        // 尝试将字符串转换为数字
                        double numericValue = Double.parseDouble(bindderInfoMap.get(string).toString());
                        sixCell.setCellValue(numericValue);
                    } catch (NumberFormatException e) {
                        // 如果转换失败，可以设置错误信息或者默认值
                        sixCell.setCellValue((double) 0);
                    }
                    //sixCell.setCellValue(bindderInfoMap.get(string).toString());
                }
            }

           /* Cell sixCell = row.getCell(expertList.size() + 2);
            if (sixCell == null) {
                sixCell = row.createCell(expertList.size() + 2);
            }
            getStartAndEndCoordinates(expertList, 3);
            //sixCell.setCellFormula("AVERAGE(C6:D6)");
            StringBuilder formulaBuilder = new StringBuilder("ROUND(AVERAGE(");
            char startColumn = (char) ('B' + 1); // 从第三列开始，即'C'
            char endColumn = (char) (startColumn + expertList.size() - 1); // 计算结束列
            formulaBuilder.append(startColumn).append(6 + i).append(':').append(endColumn).append(6 + i).append("), 2)");
            String dynamicFormula = formulaBuilder.toString();
            // 设置动态生成的AVERAGE公式
            sixCell.setCellFormula(dynamicFormula);*/


        }

        // 计算新行的行号
        int newRowNumber = 5 + busiBidderInfos.size() + 2;
        // 创建新行
        Row newRow = sheet.createRow(newRowNumber);
        // 在第二列（索引为1）创建一个单元格
        Cell cell = newRow.createCell(1);
        // 设置单元格的值
        cell.setCellValue("评审专家签字：");
        return sheet;
    }

    //投标报价得分表
    public Sheet sheet7(Long projectId, Long resultId, Workbook workbook) throws IOException {
        Sheet sheet = workbook.createSheet("投标报价得分表");
        //查询导出行所需要的数据信息
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>()
                .eq("project_id", projectId));
        //审计行标题
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                //.eq("item_mode", "2")
                .eq("item_code", "tbbjdf")
        );
        List<BusiBidderInfo> busiBidderInfos = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>().eq("project_id", projectId)
                .eq("decode_flag",1).eq("is_abandoned_bid",0));

        List<Long> xwqy = new ArrayList<>();
        for (BusiBidderInfo busiBidderInfo : busiBidderInfos) {
            EvalExpertEvaluationDetail evalExpertEvaluationDetail = evalExpertEvaluationDetailService.getOne(new QueryWrapper<EvalExpertEvaluationDetail>()
                    .eq("scoring_method_uitem_id", scoringMethodItems.get(0).getScoringMethodItemId())
                    .eq("expert_result_id", resultId)
                    .eq("ent_id", busiBidderInfo.getBidderId())
                    .eq("remark", "xwqy")
            );
            if (null != evalExpertEvaluationDetail) {
                xwqy.add(busiBidderInfo.getBidderId());
            }
        }

        GetEvalAgainQuoteVo getEvalAgainQuoteVo = new GetEvalAgainQuoteVo();
        getEvalAgainQuoteVo.setItemId(scoringMethodItems.get(0).getScoringMethodItemId());
        getEvalAgainQuoteVo.setResultId(resultId);
        getEvalAgainQuoteVo.setProjectId(projectId);
        getEvalAgainQuoteVo.setXwqys(xwqy);

        Map<String, Object> resultMap = evalAgainQuoteService.getEvalAgainQuote(getEvalAgainQuoteVo);
        Map<Long, Map<String, BigDecimal>> tableData = (Map<Long, Map<String, BigDecimal>>) resultMap.get("resultMap");
        List<String> tableColumns = (ArrayList) resultMap.get("col");
        System.out.println(JSONObject.toJSONString(tableColumns));
        System.out.println(JSONObject.toJSONString(tableData));

        //总计首页标题需要合并多少列（0，0，0，num）+1是指的合计列
        // Integer num = calculateScoringMethodItemSizes(scoringMethodItems)+1;
        Integer num = tableColumns.size() + 2;
        System.out.println("合并多少列" + num);
        if (num == null || num < 0) {
            throw new IllegalArgumentException("Invalid number of columns to merge: " + num);
        }
        //通过项目id查询项目信息、专家信息及开标记录表
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日HH点mm分");
        String formattedDate = formatter.format(evaluationInfo.getEvaluationTime());
        // 指定要合并的列数
        //sheet.setColumnWidth(0, 60 * 256); // 第一列宽度
        ExcelStyleHelper styleHelper = new ExcelStyleHelper(workbook);
        // 创建行并设置行高和内容
        for (int i = 0; i < (5 + busiBidderInfos.size()); i++) {
            Row row = sheet.createRow(i);
            row.setHeightInPoints(i < 5 ? 40 : 60); // 设置行高，假设前四行高度为20，其余为40
            for (int j = 0; j < num; j++) {
                Cell cell = row.createCell(j);
                sheet.setColumnWidth(j, 15 * 256);
                if (i == 0) {
                    cell.setCellStyle(styleHelper.createHeaderCellStyle());
                } else if (i < 4) {
                    cell.setCellStyle(styleHelper.createTitleCellStyle());
                }
//                else if (i == 4) {
//                    cell.setCellStyle(styleHelper.createTitleRightCellStyle());
//                }
                else {
                    cell.setCellStyle(styleHelper.createContentCellStyle());
                }
                setBorderStyle(cell.getCellStyle(), workbook); // 设置单元格边框样式
            }
        }
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            firstRow = sheet.createRow(0);
        }
        Cell firstCell = firstRow.getCell(0);
        if (firstCell == null) {
            firstCell = firstRow.createCell(0);
        }
        firstCell.setCellValue("投标报价得分表");
        // 合并第一行的单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, num - 1));

        //合并序列和供应商名称
        for (int i = 1; i <= 6; i++) {
            Row row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            Cell secondCell = row.getCell(1);
            if (cell == null) {
                cell = row.createCell(1);
            }
            // 合并单元格
            // 根据行号设置不同的值
            switch (i) {
                case 1:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目名称：" + tenderProject.getProjectName());
                    break;
                case 2:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目编号：" + tenderProject.getProjectCode());
                    break;
                case 3:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("评标日期：" + formattedDate);
                    break;
                case 4:
                    // 合并第五行的第一列
                    //sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 0, 0));
                    cell.setCellValue("序号");
                    // 合并第五行的第二列
                    //sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 1, 1));
                    secondCell.setCellValue("专家名称");
                    for (int j = 0; j < tableColumns.size(); j++) {
                        Cell jCell = row.getCell(j + 2);
                        if (jCell == null) {
                            jCell = row.createCell(j + 2);
                        }
                        //BusiExtractExpertResult busiExtractExpertResult1 = JSONObject.parseObject(JSONObject.toJSONString(tableColumns.get(j)), BusiExtractExpertResult.class);

                        jCell.setCellValue(tableColumns.get(j));
                    }
//                    Cell jCell = row.getCell(tableColumns.size()+2);
//                    if (jCell == null) {
//                        jCell = row.createCell(tableColumns.size()+2);
//                    }
//                    jCell.setCellValue("平均值");
                    break;
            }

        }

        for (int i = 0; i < busiBidderInfos.size(); i++) {
            //每个供应商是一行
            Row row = sheet.getRow(5 + i);
            if (row == null) {
                row = sheet.createRow(5 + i);
            }
            //序号列
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            cell.setCellValue(i + 1);
            //供应商名称列
            Cell twoCell = row.getCell(1);
            if (twoCell == null) {
                twoCell = row.createCell(1);
            }
            BusiBidderInfo busiBidderInfo = busiBidderInfos.get(i);
            twoCell.setCellValue(busiBidderInfo.getBidderName());


            //Map<String, Map<String, Object>> entResult = new HashMap<>();
            for (int j = 0; j < tableColumns.size(); j++) {
                Map<String, BigDecimal> map = tableData.get(busiBidderInfo.getBidderId());
                Cell sixCell = row.getCell(j + 2);
                if (sixCell == null) {
                    sixCell = row.createCell(j + 2);
                }
                String s = tableColumns.get(j);
                sixCell.setCellValue(map.get(s).toString());
            }
        }
        // 计算新行的行号
        int newRowNumber = 4 + busiBidderInfos.size() + 2;
        // 创建新行
        Row newRow = sheet.createRow(newRowNumber);
        // 在第二列（索引为1）创建一个单元格
        Cell cell = newRow.createCell(1);
        // 设置单元格的值
        cell.setCellValue("评审专家签字：");
        return sheet;
    }

    //磋商评标汇总表
    public Sheet sheet8(Long projectId, Long resultId, Workbook workbook) throws IOException {
        Sheet sheet = workbook.createSheet("评标汇总表");
        //查询导出行所需要的数据信息
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>()
                .eq("project_id", projectId));
        //审计行标题
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(new QueryWrapper<ScoringMethodItem>()
                        .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                        .eq("item_mode", "1")
                //.eq("item_code", "tbbjdf")
        );


        Map<String, Object> reviewSummary = evalExpertEvaluationDetailService.getReviewSummary(projectId, resultId);

        List<BusiBidderInfo> busiBidderInfos = (List<BusiBidderInfo>) reviewSummary.get("busiBidderInfos");
        List<ScoringMethodItem> scoringMethodItemList = (ArrayList) reviewSummary.get("scoringMethodItems");
        Map<Long, Map<Long, String>> resultMap= (Map<Long, Map<Long, String>>) reviewSummary.get("resultMap");

        //总计首页标题需要合并多少列（0，0，0，num）+1是指的合计列
        // Integer num = calculateScoringMethodItemSizes(scoringMethodItems)+1;
        Integer num = scoringMethodItemList.size() + 2;
        System.out.println("合并多少列" + num);
        if (num == null || num < 0) {
            throw new IllegalArgumentException("Invalid number of columns to merge: " + num);
        }
        //通过项目id查询项目信息、专家信息及开标记录表
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日HH点mm分");
        String formattedDate = formatter.format(evaluationInfo.getEvaluationTime());
        // 指定要合并的列数
        // sheet.setColumnWidth(0, 60 * 256); // 第一列宽度
        ExcelStyleHelper styleHelper = new ExcelStyleHelper(workbook);
        CellStyle titleRowCellStyle = styleHelper.createTitleRowCellStyle();
        // 创建行并设置行高和内容
        for (int i = 0; i < (5 + busiBidderInfos.size() + busiBidderInfos.size()); i++) {
            Row row = sheet.createRow(i);
            row.setHeightInPoints(i < 5 ? 40 : 60); // 设置行高，假设前四行高度为20，其余为40
            for (int j = 0; j < num; j++) {
                Cell cell = row.createCell(j);
                sheet.setColumnWidth(j, 15 * 256);

                if (i == 0) {
                    cell.setCellStyle(styleHelper.createHeaderCellStyle());
                } else if (i < 4) {
                    cell.setCellStyle(styleHelper.createTitleCellStyle());
                }
//                else if (i == 4) {
//                    cell.setCellStyle(styleHelper.createTitleRightCellStyle());
//                }
                else {
                    cell.setCellStyle(styleHelper.createContentCellStyle());
                }
                setBorderStyle(cell.getCellStyle(), workbook); // 设置单元格边框样式
            }
        }
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            firstRow = sheet.createRow(0);
        }
        Cell firstCell = firstRow.getCell(0);
        if (firstCell == null) {
            firstCell = firstRow.createCell(0);
        }
        firstCell.setCellValue("评标汇总表");
        // 合并第一行的单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, num - 1));

        //合并序列和供应商名称
        for (int i = 1; i <= 6; i++) {
            Row row = sheet.getRow(i);
            sheet.setColumnWidth(i, 20 * 256);
            if (row == null) {
                row = sheet.createRow(i);
            }
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            Cell secondCell = row.getCell(1);
            if (cell == null) {
                cell = row.createCell(1);
            }
            // 合并单元格
            // 根据行号设置不同的值
            switch (i) {
                case 1:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目名称：" + tenderProject.getProjectName());
                    break;
                case 2:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目编号：" + tenderProject.getProjectCode());
                    break;
                case 3:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("评标日期：" + formattedDate);
                    break;
                case 4:
                    // 合并第五行的第一列
                    //sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 0, 0));
                    cell.setCellValue("序号");
                    // 合并第五行的第二列
                    //sheet.addMergedRegion(new CellRangeAddress(i, i + 2, 1, 1));
                    secondCell.setCellValue("供应商名称");
                    for (int j = 0; j < scoringMethodItemList.size(); j++) {
                        Cell jCell = row.getCell(j + 2);
                        if (jCell == null) {
                            jCell = row.createCell(j + 2);
                        }
                        jCell.setCellValue(scoringMethodItemList.get(j).getItemName());
                    }
                    break;

            }

        }
        Map<String, String> longMap = new HashMap<>();
        for (int i = 0; i < busiBidderInfos.size(); i++) {
            //每个供应商是一行
            Row row = sheet.getRow(5 + i);
            if (row == null) {
                row = sheet.createRow(5 + i);
            }
            //序号列
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            cell.setCellValue(i + 1);
            //供应商名称列
            Cell twoCell = row.getCell(1);
            if (twoCell == null) {
                twoCell = row.createCell(1);
            }
            BusiBidderInfo busiBidderInfo = busiBidderInfos.get(i);
            twoCell.setCellValue(busiBidderInfo.getBidderName());
            for (int j = 0; j < scoringMethodItemList.size(); j++) {
                Map<Long, String> map = (Map<Long, String>) resultMap.get(busiBidderInfo.getBidderId());
                Cell sixCell = row.getCell(j + 2);
                if (sixCell == null) {
                    sixCell = row.createCell(j + 2);
                }
                Long s = scoringMethodItemList.get(j).getScoringMethodItemId();
                if (s.equals(1L)) {
                    sixCell.setCellValue(map.get(s));
                    longMap.put(busiBidderInfo.getBidderId().toString(), map.get(s));
                } else {
                    sixCell.setCellValue(map.get(s));
                }
            }
        }
        //标题5+供应商值3
        int margenStartNumber = 5 + busiBidderInfos.size();
        int margenEndNumber = 5 + busiBidderInfos.size() + busiBidderInfos.size() - 1;

        sheet.addMergedRegion(new CellRangeAddress(margenStartNumber, margenEndNumber, 0, 1));


        // 将Map的条目转移到List中
        List<Map.Entry<String, String>> list = new ArrayList<>(longMap.entrySet());

        // 对List进行排序，这里使用的是降序排序
        // 对List进行排序，将value转成Double后根据Double值进行降序排序
        Collections.sort(list, new Comparator<Map.Entry<String, String>>() {
            @Override
            public int compare(Map.Entry<String, String> o1, Map.Entry<String, String> o2) {
                Double value1 = Double.parseDouble(o1.getValue());
                Double value2 = Double.parseDouble(o2.getValue());
                return value2.compareTo(value1);
            }
        });

        // 打印排序后的List

        for (int i = 0; i < list.size(); i++) {
            Row row = sheet.getRow(margenStartNumber + i);
            if (row == null) {
                row = sheet.createRow(margenStartNumber + i);
            }
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            cell.setCellValue("推荐中标候选人");


            sheet.addMergedRegion(new CellRangeAddress(margenStartNumber + i, margenStartNumber + i, 2, num - 1));

            Cell cellj = row.getCell(2);
            if (cellj == null) {
                cellj = row.createCell(2);
            }
            // 使用Stream API将List转换为Map，其中键是bidderId，值是BusiBidderInfo对象
            Map<Long, BusiBidderInfo> bidderInfoMap = busiBidderInfos.stream()
                    .filter(bidder -> bidder.getIsAbandonedBid() == 0) // 过滤出 isAbandonedBid=0 的元素
                    .collect(Collectors.toMap(BusiBidderInfo::getBidderId, bidderInfo -> bidderInfo));

            Long key = Long.parseLong(list.get(i).getKey());
            System.out.println(key);
            cellj.setCellValue("第"+(i+1)+"名" + bidderInfoMap.get(key).getBidderName());
        }

        // 计算新行的行号
        int newRowNumber = 4 + busiBidderInfos.size() + busiBidderInfos.size() + 2;
        // 创建新行
        Row newRow = sheet.createRow(newRowNumber);
        // 在第二列（索引为1）创建一个单元格
        Cell cell = newRow.createCell(1);
        // 设置单元格的值
        cell.setCellValue("评审专家签字：");
        return sheet;
    }

    //询价
    //评标汇总表
    public Sheet sheet9(Long projectId, Long resultId, Workbook workbook) throws IOException {
        Sheet sheet = workbook.createSheet("询价评标汇总表");
        Map<String, Object> reviewSummary = evalExpertEvaluationDetailService.getReviewSummary(projectId, resultId);
        List<BusiBidderInfo> busiBidderInfos = (List<BusiBidderInfo>) reviewSummary.get("busiBidderInfos");
        Map<Long, String> bidNameMap = busiBidderInfos.stream()
                .collect(Collectors.toMap(
                        BusiBidderInfo::getBidderId,
                        BusiBidderInfo::getBidderName
                ));
        Integer num = 1+ 2;
        System.out.println("合并多少列" + num);
        if (num == null || num < 0) {
            throw new IllegalArgumentException("Invalid number of columns to merge: " + num);
        }
        //通过项目id查询项目信息、专家信息及开标记录表
        BusiTenderProject tenderProject = iBusiTenderProjectService.getById(projectId);
        EvalProjectEvaluationInfo evaluationInfo = evalProjectEvaluationInfoService.getOne(new QueryWrapper<EvalProjectEvaluationInfo>().eq("project_id", projectId));
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日HH点mm分");
        String formattedDate = formatter.format(evaluationInfo.getEvaluationTime());
        // 指定要合并的列数
        ExcelStyleHelper styleHelper = new ExcelStyleHelper(workbook);
        // 创建行并设置行高和内容
        for (int i = 0; i < (5 + busiBidderInfos.size() + busiBidderInfos.size()); i++) {
            Row row = sheet.createRow(i);
            row.setHeightInPoints(i < 5 ? 20 : 40); // 设置行高，假设前四行高度为20，其余为40
            for (int j = 0; j < num; j++) {
                Cell cell = row.createCell(j);
                sheet.setColumnWidth(j, 15 * 256);

                if (i == 0) {
                    cell.setCellStyle(styleHelper.createHeaderCellStyle());
                } else if (i < 4) {
                    cell.setCellStyle(styleHelper.createTitleCellStyle());
                }else {
                    cell.setCellStyle(styleHelper.createContentCellStyle());
                }
                setBorderStyle(cell.getCellStyle(), workbook); // 设置单元格边框样式
            }
        }
        Row firstRow = sheet.getRow(0);
        if (firstRow == null) {
            firstRow = sheet.createRow(0);
        }
        Cell firstCell = firstRow.getCell(0);
        if (firstCell == null) {
            firstCell = firstRow.createCell(0);
        }
        firstCell.setCellValue("评标汇总表");
        // 合并第一行的单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, num - 1));
        //合并序列和供应商名称
        for (int i = 1; i <= 6; i++) {
            Row row = sheet.getRow(i);
            sheet.setColumnWidth(i, 20 * 256);
            if (row == null) {
                row = sheet.createRow(i);
            }
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            Cell secondCell = row.getCell(1);
            if (secondCell == null) {
                secondCell = row.createCell(1);
            }
            Cell threeCell = row.getCell(2);
            if (threeCell == null) {
                threeCell = row.createCell(2);
            }
            // 合并单元格
            // 根据行号设置不同的值
            switch (i) {
                case 1:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目名称：" + tenderProject.getProjectName());
                    break;
                case 2:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("项目编号：" + tenderProject.getProjectCode());
                    break;
                case 3:
                    sheet.addMergedRegion(new CellRangeAddress(i, i, 0, num - 1));
                    cell.setCellValue("评标日期：" + formattedDate);
                    break;
                case 4:
                    cell.setCellValue("序号");
                    secondCell.setCellValue("供应商名称");
                    threeCell.setCellValue("报价金额");
                    break;
            }
        }
        //询价没有二次报价，所以只查第一次的价格
        List<BusiBiddingRecord> biddingRecords = iBusiBiddingRecordService.list(
                new QueryWrapper<BusiBiddingRecord>()
                        .eq("project_id", projectId)
                        .eq("del_flag", 0)
                        .orderByAsc("bid_amount")
        );
        //重新打分-小微企业分数计算
        //查询评分方法
        ScoringMethodUinfo scoringMethodUinfo = scoringMethodUinfoService.getOne(new QueryWrapper<ScoringMethodUinfo>().eq("project_id", projectId));
        List<ScoringMethodItem> scoringMethodItems = scoringMethodItemService.list(
                new QueryWrapper<ScoringMethodItem>()
                        .eq("item_code", "tbbjdf")
                        .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
        );
        ScoringMethodUitem one = scoringMethodUitemService.getOne(new QueryWrapper<ScoringMethodUitem>()
                .eq("scoring_method_id", scoringMethodUinfo.getScoringMethodId())
                .eq("scoring_method_item_id", scoringMethodItems.get(0).getScoringMethodItemId())
                .eq("ent_method_id",scoringMethodUinfo.getEntMethodId())
        );
        // 使用Stream API提取bidderId并收集到Set中
        List<Long> bidderIds = busiBidderInfos.stream()
                .map(BusiBidderInfo::getBidderId)
                .collect(Collectors.toList());

        QueryWrapper<EvalExpertEvaluationDetail> queryWrapper=new QueryWrapper();
        queryWrapper.eq("scoring_method_uitem_id",one.getEntMethodItemId());
        queryWrapper.eq("expert_result_id",resultId);
//                queryWrapper.in("ent_id",bidderIds);
        queryWrapper.inSql("ent_id", "SELECT bidder_id FROM busi_bidder_info WHERE project_id="+projectId+" AND is_abandoned_bid=0");
        queryWrapper.orderByAsc("evaluation_result");
        List<EvalExpertEvaluationDetail> evalExpertEvaluationDetails = evalExpertEvaluationDetailService.list(queryWrapper);
        // 对列表进行排序
        Collections.sort(evalExpertEvaluationDetails, new Comparator<EvalExpertEvaluationDetail>() {
            @Override
            public int compare(EvalExpertEvaluationDetail o1, EvalExpertEvaluationDetail o2) {
                // 假设evaluationResult是一个表示金额的字符串，例如 "$1,234.56"
                // 需要移除货币符号和千位分隔符，然后转换为BigDecimal
                BigDecimal amount1 = new BigDecimal(o1.getEvaluationResult().replaceAll("[^\\d.]", ""));
                BigDecimal amount2 = new BigDecimal(o2.getEvaluationResult().replaceAll("[^\\d.]", ""));
                return amount1.compareTo(amount2);
            }
        });

        // 如果您使用Java 8或更高版本，可以使用lambda表达式简化代码
        evalExpertEvaluationDetails.sort(Comparator.comparing(o -> new BigDecimal(o.getEvaluationResult().replaceAll("[^\\d.]", ""))));

        // 转换为 Map<biddingId, bidAmount>
        Map<Long, BigDecimal> bidAmountMap = biddingRecords.stream()
                .collect(Collectors.toMap(
                        BusiBiddingRecord::getBidderId, // Key: biddingId
                        BusiBiddingRecord::getBidAmount // Value: bidAmount
                ));


        for (int i = 0; i < evalExpertEvaluationDetails.size(); i++) {
            //每个供应商是一行
            Row row = sheet.getRow(5 + i);
            if (row == null) {
                row = sheet.createRow(5 + i);
            }
            //序号列
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            cell.setCellValue(i + 1);
            //供应商名称列
            Cell twoCell = row.getCell(1);
            if (twoCell == null) {
                twoCell = row.createCell(1);
            }
            Long bidder=evalExpertEvaluationDetails.get(i).getEntId();
            // 查找对应的BusiBidderInfo对象
            BusiBidderInfo bidderInfo = busiBidderInfos.stream()
                    .filter(b -> b.getBidderId().equals(bidder))
                    .findFirst()
                    .orElse(null);
            twoCell.setCellValue(bidderInfo.getBidderName());
            Cell threeCell = row.getCell(2);
            if (twoCell == null) {
                threeCell = row.createCell(2);
            }
            threeCell.setCellValue(bidAmountMap.get(bidder).toString());
        }
        //标题5+供应商值3
        int margenStartNumber = 5 + busiBidderInfos.size();
        int margenEndNumber = 5 + busiBidderInfos.size() + busiBidderInfos.size() - 1;
        sheet.addMergedRegion(new CellRangeAddress(margenStartNumber, margenEndNumber, 0, 1));

        // 将 Map 转换为 List 并按价格升序排序
        List<Map.Entry<Long, BigDecimal>> sortedEntries = bidAmountMap.entrySet().stream()
                .sorted(Comparator.comparing(Map.Entry::getValue)) // 升序排序
                .collect(Collectors.toList());
    /*    List<Map.Entry<Long, BigDecimal>> sortedEntries = bidAmountMap.entrySet().stream()
                .sorted(Comparator.comparing(Map.Entry::getValue).reversed()) // 降序排序
                .collect(Collectors.toList());
*/


        // 取出前三名
        List<Map.Entry<Long, BigDecimal>> topThree = sortedEntries.stream()
                .limit(3) // 只取前三个
                .collect(Collectors.toList());

        // 判断前两个价格是否相同
        boolean isFirstTwoEqual = topThree.size() >= 2 &&
                topThree.get(0).getValue().compareTo(topThree.get(1).getValue()) == 0;

        for (int i = 0; i < topThree.size(); i++) {
            Row row = sheet.getRow(margenStartNumber + i);
            if (row == null) {
                row = sheet.createRow(margenStartNumber + i);
            }

            // 设置“推荐中标候选人”单元格
            Cell cell = row.getCell(0);
            if (cell == null) {
                cell = row.createCell(0);
            }
            cell.setCellValue("推荐中标候选人");

            // 设置名次和供应商名称
            Cell cellj = row.getCell(2);
            if (cellj == null) {
                cellj = row.createCell(2);
            }

            if (isFirstTwoEqual) {
                // 前两名并列第一名
                if (i == 0 || i == 1) {
                    cellj.setCellValue("第1名" + bidNameMap.get(evalExpertEvaluationDetails.get(i).getEntId()));
                } else if (i == 2) {
                    cellj.setCellValue("第2名" + bidNameMap.get(evalExpertEvaluationDetails.get(i).getEntId()));
                }
            } else {
                // 正常情况
                cellj.setCellValue("第" + (i + 1) + "名" + bidNameMap.get(evalExpertEvaluationDetails.get(i).getEntId()));
            }
        }
        // 计算新行的行号
        int newRowNumber = 4 + busiBidderInfos.size() + busiBidderInfos.size() + 2;
        // 创建新行
        Row newRow = sheet.createRow(newRowNumber);
        // 在第二列（索引为1）创建一个单元格
        Cell cell = newRow.createCell(1);
        // 设置单元格的值
        cell.setCellValue("评审专家签字：");
        return sheet;
    }

    /**
     * 计算标题行合并列数
     *
     * @param scoringMethodItems
     * @return
     */
    public static Integer calculateScoringMethodItemSizes(List<ScoringMethodItem> scoringMethodItems) {
        Integer size = 0;
        for (ScoringMethodItem item : scoringMethodItems) {
            // 计算每个ScoringMethodItem的UItems.size + 2
            size += item.getUItems().size();
        }
        //序号和供应商名称列
        size += 2;
        return size;
    }

    public static Map<Long, Map<String, Integer>> generateResultMap(List<ScoringMethodItem> scoringMethodItems) {
        Map<Long, Map<String, Integer>> resultMap = new HashMap<>();

        int startNum = 2; // 起始序号
        for (ScoringMethodItem scoringMethodItem : scoringMethodItems) {
            Map<String, Integer> map = new HashMap<>();
            map.put("startNum", startNum);
            int entNum = startNum + scoringMethodItem.getUItems().size() - 1;
            map.put("entNum", entNum);
            resultMap.put(scoringMethodItem.getScoringMethodItemId(), map);
            startNum = entNum + 1; // 更新下一个ScoringMethodItem的起始序号
        }

        return resultMap;
    }

    /**
     * *求表格中的字母坐标
     *
     * @param list
     * @param startColumnNumber
     * @return
     */

    public static Map<String, String> getStartAndEndCoordinates(List<?> list, int startColumnNumber) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("List must not be null or empty");
        }
        if (startColumnNumber < 1) {
            throw new IllegalArgumentException("Start column number must be greater than 0");
        }

        Map<String, String> coordinateMap = new HashMap<>();
        String startCoordinate = getExcelColumnName(startColumnNumber) + "1";
        String endCoordinate = getExcelColumnName(startColumnNumber + list.size() - 1) + "1";

        coordinateMap.put("start", startCoordinate);
        coordinateMap.put("end", endCoordinate);

        return coordinateMap;
    }

    private static String getExcelColumnName(int columnNumber) {
        if (columnNumber < 1) {
            throw new IllegalArgumentException("Column number must be greater than 0");
        }

        StringBuilder columnName = new StringBuilder();
        int dividend = columnNumber;
        int modulo;

        while (dividend > 0) {
            modulo = (dividend - 1) % 26;
            columnName.insert(0, (char) (modulo + 'A'));
            dividend = (dividend - modulo) / 26;
        }

        return columnName.toString();
    }


}
