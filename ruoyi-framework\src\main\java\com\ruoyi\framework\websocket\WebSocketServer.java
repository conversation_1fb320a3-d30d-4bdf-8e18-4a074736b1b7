package com.ruoyi.framework.websocket;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.domain.BusiBidderInfo;
import com.ruoyi.busi.domain.BusiExtractExpertResult;
import com.ruoyi.busi.domain.BusiOpenMessageRecord;
import com.ruoyi.busi.service.IBusiBidderInfoService;
import com.ruoyi.busi.service.IBusiExtractExpertResultService;
import com.ruoyi.busi.service.IBusiOpenMessageRecordService;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.eval.domain.EvalInquiringBidInfo;
import com.ruoyi.eval.domain.EvalProjectEvaluationInfo;
import com.ruoyi.eval.service.IEvalInquiringBidInfoService;
import com.ruoyi.eval.service.IEvalProjectEvaluationInfoService;
import com.ruoyi.framework.websocket.config.CustomSpringConfigurator;
import com.ruoyi.framework.websocket.util.SemaphoreUtils;
import com.ruoyi.framework.websocket.vo.WebSocketMessageVo;
import com.ruoyi.framework.websocket.vo.WebSocketUsers;
import okhttp3.WebSocket;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.Session;
import javax.websocket.OnOpen;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Date;
import java.util.concurrent.Semaphore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
/**
 * websocket 消息处理
 *
 * <AUTHOR>
 */
@Component
@ServerEndpoint(value = "/websocket/message/{userId}/{projectId}/{type}", configurator = CustomSpringConfigurator.class)
public class WebSocketServer {
    @Autowired
    private IEvalInquiringBidInfoService evalInquiringBidInfoService;
    @Autowired
    private IBusiExtractExpertResultService busiExtractExpertResultService;
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IEvalProjectEvaluationInfoService evalProjectEvaluationInfoService;
    /**
     * WebSocketServer 日志控制器
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(WebSocket.class);

    /**
     * 默认最多允许同时在线人数100
     */
    public static int socketMaxOnlineCount = 100;

    private static Semaphore socketSemaphore = new Semaphore(socketMaxOnlineCount);

    @Resource
    private IBusiOpenMessageRecordService busiOpenMessageRecordService;

    @Resource
    private IBaseEntInfoService iBaseEntInfoService;

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "userId") String userId,@PathParam(value = "type") Integer type, @PathParam(value = "projectId") String projectId) throws Exception {
        boolean semaphoreFlag = false;
        // 尝试获取信号量
        semaphoreFlag = SemaphoreUtils.tryAcquire(socketSemaphore);
        if (!semaphoreFlag) {
            // 未获取到信号量
            LOGGER.error("\n 当前在线人数超过限制数- {}", socketMaxOnlineCount);
            WebSocketUsers.sendMessageToUserByText(session, "当前在线人数超过限制数：" + socketMaxOnlineCount);
            session.close();
        } else {
            if (type==0){
                // 添加用户
                WebSocketUsers.put(userId + "_" + projectId+ "_"+type, session);
//                BusiOpenMessageRecord busiOpenMessageRecord = new BusiOpenMessageRecord();
//                busiOpenMessageRecord.setSendId(Long.parseLong(userId));
//                busiOpenMessageRecord.setProjectId(Long.parseLong(projectId));
//                busiOpenMessageRecord.setContent(userId + "：上线了");
//                busiOpenMessageRecord.setType(0);
//                busiOpenMessageRecord.setSendTime(new Date());
//                busiOpenMessageRecord.setDelFlag(0);
//                busiOpenMessageRecord.setStatus(0);
//                busiOpenMessageRecordService.save(busiOpenMessageRecord);
                //busiOpenMessageRecordService.list();
                LOGGER.info("\n 建立连接 - {}", session);
                LOGGER.info("\n 当前人数 - {}", WebSocketUsers.getUsers().size());
                WebSocketUsers.sendMessageToUserByText(session, "连接成功");
            }else  if (type==1){
                WebSocketUsers.put(userId + "_" + projectId+ "_"+type, session);
                WebSocketUsers.sendMessageToUserByText(session, "连接成功");
            }
        }
    }

    /**
     * 连接关闭时处理
     */
    @OnClose
    public void onClose(Session session, @PathParam(value = "userId") String userId, @PathParam(value = "type") Integer type, @PathParam(value = "projectId") String projectId) {
        if (type==0){
            LOGGER.info("\n 关闭连接 - {}", session);
//            BusiOpenMessageRecord busiOpenMessageRecord = new BusiOpenMessageRecord();
//            busiOpenMessageRecord.setSendId(Long.parseLong(userId));
//            busiOpenMessageRecord.setProjectId(Long.parseLong(projectId));
//            busiOpenMessageRecord.setContent(userId + "：下线了");
//            busiOpenMessageRecord.setType(0);
//            busiOpenMessageRecord.setSendTime(new Date());
//            busiOpenMessageRecord.setDelFlag(0);
//            busiOpenMessageRecord.setStatus(0);
//            busiOpenMessageRecordService.save(busiOpenMessageRecord);
            // 移除用户
            boolean removeFlag = WebSocketUsers.remove(userId + "_" + projectId+ "_"+type);
            if (!removeFlag) {
                // 获取到信号量则需释放
                SemaphoreUtils.release(socketSemaphore);
            }
        }else if (type==1){
            boolean removeFlag = WebSocketUsers.remove(userId + "_" + projectId+ "_"+type);
            if (!removeFlag) {
                // 获取到信号量则需释放
                SemaphoreUtils.release(socketSemaphore);
            }        }

    }

    /**
     * 抛出异常时处理
     */
    @OnError
    public void onError(Session session, Throwable exception) throws Exception {
        if (session.isOpen()) {
            // 关闭连接
            session.close();
        }
        String sessionId = session.getId();
        LOGGER.info("\n 连接异常 - {}", sessionId);
        LOGGER.info("\n 异常信息 - {}", exception);
        // 移出用户
        WebSocketUsers.remove(sessionId);
        // 获取到信号量则需释放
        SemaphoreUtils.release(socketSemaphore);
    }

    /**
     * 服务器接收到客户端消息时调用的方法
     */
    @OnMessage
    public void onMessage(@PathParam(value = "userId") String userId, @PathParam(value = "type") Integer type,@PathParam(value = "projectId") String projectId, String message, Session session) throws IOException {
        if (type==0){
            BusiOpenMessageRecord busiOpenMessageRecord = new BusiOpenMessageRecord();
            busiOpenMessageRecord.setSendId(Long.parseLong(userId));
            BaseEntInfo byId = iBaseEntInfoService.getById(userId);
            if (null!=byId){
                busiOpenMessageRecord.setSendName(byId.getEntName());
            }
            busiOpenMessageRecord.setProjectId(Long.parseLong(projectId));
            busiOpenMessageRecord.setContent(message);
            busiOpenMessageRecord.setType(3);
            busiOpenMessageRecord.setSendTime(new Date());
            busiOpenMessageRecord.setDelFlag(0);
            busiOpenMessageRecord.setStatus(0);
            List<String> stringList = new ArrayList<>();
            stringList.add("ping");
            stringList.add("signIn");
            stringList.add("ready");
            stringList.add("bidPublicity");
            stringList.add("supDecrytion");
            stringList.add("nextStep");
            stringList.add("bidAnnouncement");
            stringList.add("decryption");
            stringList.add("end");
            stringList.add("flowLabel");
            stringList.add("evalAgainQuote");
            if (stringList.stream().anyMatch(message::contains)) {
//            WebSocketUsers.sendMessageToUserByText(session, message);
                WebSocketUsers.sendMessageToUsersByText(message);
            } else {
                busiOpenMessageRecordService.save(busiOpenMessageRecord);
                WebSocketUsers.sendMessageToUsersByText(JSONObject.toJSONString(busiOpenMessageRecord));
                // WebSocketUsers.sendMessageToUserByText(session, JSONObject.toJSONString(busiOpenMessageRecord));
            }
        }else if (type==1){
            List<String> stringList = new ArrayList<>();
            stringList.add("ping");
            if (stringList.stream().anyMatch(message::contains)) {
                WebSocketUsers.sendMessageToUsersByText(message);
            }else {
                WebSocketMessageVo webSocketMessageVo=JSONObject.parseObject(message,WebSocketMessageVo.class);
                if (webSocketMessageVo != null && webSocketMessageVo.getEvalInquiringBidInfo() != null
                        && webSocketMessageVo.getEvalInquiringBidInfo().getInquiringBidId() != null) {
                    // inquiringBidId不为空，可以继续你的操作
                    Long inquiringBidId = webSocketMessageVo.getEvalInquiringBidInfo().getInquiringBidId();
                    EvalInquiringBidInfo evalInquiringBidInfo = evalInquiringBidInfoService.getById(inquiringBidId);

                    List<BusiBidderInfo> list = busiBidderInfoService.list(new QueryWrapper<BusiBidderInfo>()
                            .eq("project_id", webSocketMessageVo.getProjectId())
                            .eq("bidder_id", webSocketMessageVo.getEntId()));

                    if (evalInquiringBidInfo!=null){
                        evalInquiringBidInfo.setReplyContent(webSocketMessageVo.getEvalInquiringBidInfo().getReplyContent());
                        evalInquiringBidInfo.setReplyFile(webSocketMessageVo.getEvalInquiringBidInfo().getReplyFile());
                        evalInquiringBidInfo.setReplyTime(new Date());
                        evalInquiringBidInfo.setUpdateBy("update");
                        evalInquiringBidInfo.getParams().put("opUser", list.get(0).getBidderName());
                        evalInquiringBidInfoService.updateById(evalInquiringBidInfo);

                    }
                } else {
                    BusiExtractExpertResult byId = busiExtractExpertResultService.getById(userId);
                    // inquiringBidId为空，处理空值情况
                    webSocketMessageVo.getEvalInquiringBidInfo().setInquiringTime(new Date());
                    webSocketMessageVo.getEvalInquiringBidInfo().setCreateTime(new Date());
                    webSocketMessageVo.getEvalInquiringBidInfo().setCreateBy("create");
                    webSocketMessageVo.getEvalInquiringBidInfo().getParams().put("opUser",byId.getExpertName());

                    evalInquiringBidInfoService.save(webSocketMessageVo.getEvalInquiringBidInfo());
                    WebSocketUsers.sendMessageToUserByKey(webSocketMessageVo.getEntId() + "_" + projectId+ "_"+type ,JSONObject.toJSONString(webSocketMessageVo));
                }
                //查询该项目所有的专家信息，专家发起询价和供应商回复内容都需要推送给所有专家
                BusiExtractExpertResult busiExtractExpertResult = new BusiExtractExpertResult();
                busiExtractExpertResult.setProjectId(Long.parseLong(webSocketMessageVo.getProjectId()));
                List<BusiExtractExpertResult> busiExtractExpertResults = busiExtractExpertResultService.getZhuanJiaByProjectId(busiExtractExpertResult);
                for (BusiExtractExpertResult extractExpertResult : busiExtractExpertResults) {
                    WebSocketUsers.sendMessageToUserByKey(extractExpertResult.getResultId() + "_" + projectId+ "_"+type,JSONObject.toJSONString(webSocketMessageVo));
                }
            }
        }


    }

    public static void main(String[] args) {
        WebSocketMessageVo webSocketMessageVo=new WebSocketMessageVo();
        webSocketMessageVo.setSendId("发送人ID");
        webSocketMessageVo.setProjectId("接收人ID");
        webSocketMessageVo.setType(0);
        webSocketMessageVo.setMessage("消息内容");
        webSocketMessageVo.setExpertResultId(1l);//询价专家id
        webSocketMessageVo.setEntId(2l);//询价供应商iD
        webSocketMessageVo.setProjectEvaluationId(3l);//询标项目评审信息ID

        EvalInquiringBidInfo evalInquiringBidInfo=new EvalInquiringBidInfo();
        evalInquiringBidInfo.setEntId(2l);
        evalInquiringBidInfo.setProjectEvaluationId(3l);
        evalInquiringBidInfo.setExpertResultId(1l);
        evalInquiringBidInfo.setInquiringContent("消息内容");
        evalInquiringBidInfo.setInquiringTime(new Date());//询问时间
        evalInquiringBidInfo.setReplyContent("回复内容");
        evalInquiringBidInfo.setReplyTime(new Date());//回复时间
        evalInquiringBidInfo.setReplyFile("回复文件");
        webSocketMessageVo.setEvalInquiringBidInfo(evalInquiringBidInfo);
        System.out.println(JSONObject.toJSONString(webSocketMessageVo));
    }
}
