<template>
	<div class="main-container-one">
		<div class="left-panel">
			<div class="header-bar">
				<div class="header-title">
					<div>资格性评审</div>
					<div class="header-steps">
						<div class="steps-tip">该页面操作说明</div>
						<el-image class="steps-img" :src="helpImageList[0]" :preview-src-list="helpImageList">
						</el-image>
					</div>
				</div>
				
				<!-- 文件列表 -->
				<div class="fileList" style="width: 200px; border-right: 1px solid #e6e6e6; border-left: 1px solid #e6e6e6; padding: 10px; overflow-y: auto;">
					<div style="font-weight: bold; margin-bottom: 10px; color: #333;">响应文件附件下载</div>
					<div class="fileListInner">
						<el-card
							v-for="(item, index) in attachmentsList"
							:key="index"
							class="fileItem"
							shadow="hover"
							@click.native="downloadFile(item)"
							style="margin-bottom: 8px; cursor: pointer;"
						>
							<div style="display: flex; align-items: center; padding: 5px;">
								<i class="el-icon-document" style="margin-right: 8px; color: #409EFF;"></i>
								<span style="font-size: 12px; flex: 1; word-break: break-all;">{{ item.fileName }}</span>
								<i class="el-icon-download" style="margin-left: 8px; color: #999;"></i>
							</div>
						</el-card>
					</div>
					
				</div>
				
				<div class="header-btns">
					<el-button class="item-button" @click="goToBidInquiry">询标</el-button>
					<!-- <el-button class="item-button" @click="secondOffer">发起二次报价</el-button> -->
					<div class="header-btns-group">
						<el-button
							:class="['item-button', activeButton === 'procurement' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']"
							:disabled="isAnyPdfRendering()"
							@click="showProcurementFile">采购文件</el-button>
						<el-button
							:class="['item-button', activeButton === 'response' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']"
							:disabled="isAnyPdfRendering()"
							@click="showResponseFile">响应文件</el-button>
						<el-button
							:class="['item-button', activeButton === 'contrast' ? 'qualification-blue-btn-active' : 'qualification-blue-btn']"
							:disabled="isAnyPdfRendering()"
							@click="showFileContrast">对比</el-button>
					</div>
				</div>
			</div>
			<div style="height:82%">
				<!-- PDF预览区域 -->
				<div class="pdf-container">
					<div v-show="isProcurementVisible" :class="['pdf-view', { 'border-right': isDoubleView }]">
<!--						<pdfView ref="procurement" :pdfurl="procurementPdfUrl" :uni_key="'procurement'"></pdfView>-->
						
						<PdfViewImproved
							ref="procurement"
							:pdfurl="procurementPdfUrl"
							:page-height="800"
							:buffer-size="2"
							@render-status-change="(status) => handlePdfRenderStatusChange(status, 'procurement')"
						/>
					</div>
					<div v-show="isResponseVisible" :class="['pdf-view', { 'border-left': isDoubleView }]">
<!--						<pdfView ref="response" :pdfurl="responsePdfUrl" :uni_key="'response'"></pdfView>-->
						
						<PdfViewImproved
							ref="response"
							:pdfurl="responsePdfUrl"
							:page-height="800"
							:buffer-size="2"
							@render-status-change="(status) => handlePdfRenderStatusChange(status, 'response')"
						/>
						
					</div>
				</div>
			</div>
		</div>
		<div class="divider"></div>
		<div class="right-panel">
			<div class="right-header">
				<el-select
					style="width:100%"
					v-model="selectedSupplierName"
					placeholder="请选择供应商"
					:disabled="isAnyPdfRendering()"
					@change="handleSupplierChange">
					<el-option v-for="item in supplierOptions" :key="item.bidderName" :label="item.bidderName" :value="item.bidderName">
					</el-option>
				</el-select>
			</div>
			<div class="right-content" v-if="isResponseVisible">
				<!-- PDF渲染状态提示 -->
				<div v-if="responsePdfUrl && !responsePdfRendered" class="render-status-tip">
					<i class="el-icon-loading"></i>
					<span>响应文件正在渲染中，请稍候...</span>
				</div>
				<div v-else-if="responsePdfUrl && responsePdfRendered" class="render-status-tip success">
					<i class="el-icon-success"></i>
					<span>响应文件渲染完成，可以点击跳转</span>
				</div>
				
				<div v-for="(item, index) in scoringSystem.uitems" :key="index" class="factor-item"
					@mouseenter="showFactorTooltip(item)"
					@mouseleave="hideFactorTooltip">
					<!-- 悬浮框 -->
					<div v-if="hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId"
						class="factor-tooltip"
						@mouseenter="clearTooltipTimer"
						@mouseleave="hideFactorTooltip">
						<div class="tooltip-header">
							<div class="tooltip-title">评审内容</div>
							<i class="el-icon-close tooltip-close" @click="hideFactorTooltip"></i>
						</div>
						<div class="tooltip-content" v-html="item.itemRemark"></div>
					</div>

					<div>
						<div class="factors">
							<div
								class="factor-title"
								:class="{ 'disabled': !canJumpToPage() }"
								@click="jumpToFactorPage(item)">
								{{ item.itemName }}
								<i v-if="!canJumpToPage()" class="el-icon-loading" style="margin-left: 5px; font-size: 12px;"></i>
							</div>
							<div class="factor-radio-group">
								<el-radio v-model="ratingStateMap[item.entMethodItemId].state" label="0"><span style="color:red">不通过</span></el-radio>
								<el-radio v-model="ratingStateMap[item.entMethodItemId].state" label="1"><span style="color:green">通过</span></el-radio>
							</div>
						</div>
						<el-input v-if="(ratingStateMap[item.entMethodItemId].state == 0)" class="text" type="textarea" :rows="3" placeholder="未通过原因" v-model="ratingStateMap[item.entMethodItemId].reason">
						</el-input>
						<span v-if="Object.keys(checkResult).length > 0" :style="{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }">
							<i v-if="getCheckResultState(item.itemName)==='1'" class="el-icon-success"></i>
							<i v-if="getCheckResultState(item.itemName)==='0'" class="el-icon-warning"></i>
							{{checkResultLabel[item.itemName]}}</span>
						<div class="factor-divider"></div>
					</div>
				</div>
				<div class="right-btns">
					<!-- <div><el-button class="item-button-little" style="background-color:#F5F5F5;color:#176ADB" @click="save">保存</el-button></div> -->
					<div><el-button class="item-button-little" @click="submitRating">提交</el-button></div>
				</div>
				<div class="review-content">
					<div class="review-title">评审内容：</div>
					<div class="review-html" v-html="selectedFactorNode.itemRemark"></div>
				</div>
			</div>
			
			<div class="right-content" v-else>
				<!-- PDF渲染状态提示 -->
				<div v-if="procurementPdfUrl && !procurementPdfRendered" class="render-status-tip">
					<i class="el-icon-loading"></i>
					<span>采购文件正在渲染中，请稍候...</span>
				</div>
				<div v-else-if="procurementPdfUrl && procurementPdfRendered" class="render-status-tip success">
					<i class="el-icon-success"></i>
					<span>采购文件渲染完成，可以点击跳转</span>
				</div>
				
				<div v-for="(item, index) in pageProcurement" :key="index" class="factor-item"
					@mouseenter="showFactorTooltip(item)"
					@mouseleave="hideFactorTooltip">
					<!-- 悬浮框 -->
					<div v-if="hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId"
						class="factor-tooltip"
						@mouseenter="clearTooltipTimer"
						@mouseleave="hideFactorTooltip">
						<div class="tooltip-header">
							<div class="tooltip-title">评审内容</div>
							<i class="el-icon-close tooltip-close" @click="hideFactorTooltip"></i>
						</div>
						<div class="tooltip-content" v-html="item.itemRemark"></div>
					</div>

					<div>
						<div class="factors">
							<div
								class="factor-title"
								:class="{ 'disabled': !canJumpToPage() }"
								@click="jumpToFactorPage(item)">
								{{ item.itemName }}
								<i v-if="!canJumpToPage()" class="el-icon-loading" style="margin-left: 5px; font-size: 12px;"></i>
							</div>
							<div class="factor-radio-group">
								<el-radio v-model="ratingStateMap[item.entMethodItemId].state" label="0"><span style="color:red">不通过</span></el-radio>
								<el-radio v-model="ratingStateMap[item.entMethodItemId].state" label="1"><span style="color:green">通过</span></el-radio>
							</div>
						</div>
						<el-input v-if="(ratingStateMap[item.entMethodItemId].state == 0)" class="text" type="textarea" :rows="3" placeholder="未通过原因" v-model="ratingStateMap[item.entMethodItemId].reason">
						</el-input>
						<span v-if="Object.keys(checkResult).length > 0" :style="{ color: getCheckResultState(item.itemName)=='1' ? 'green' : 'red' }">
							<i v-if="getCheckResultState(item.itemName)==='1'" class="el-icon-success"></i>
							<i v-if="getCheckResultState(item.itemName)==='0'" class="el-icon-warning"></i>
							{{checkResultLabel[item.itemName]}}</span>
						<div class="factor-divider"></div>
					</div>
				</div>
				<div class="right-btns">
					<!-- <div><el-button class="item-button-little" style="background-color:#F5F5F5;color:#176ADB" @click="save">保存</el-button></div> -->
					<div><el-button class="item-button-little" @click="submitRating">提交</el-button></div>
				</div>
				<div class="review-content">
					<div class="review-title">评审内容：</div>
					<div class="review-html" v-html="selectedFactorNode.itemRemark"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import {
	supplierInfo,
	approvalProcess,
	scoringFactors,
	checkReviewSummary,
	filesById,
} from "@/api/expert/review";
import { getDetailByPsxx } from "@/api/evaluation/detail/";
import { editEvalExpertScoreInfo } from "@/api/evaluation/expertStatus";
import { resDocReviewFactorsDecision } from "@/api/docResponse/entInfo";

export default {
	data() {
		return {
			supplierOptions: [], // 供应商下拉选项
			scoringSystem: [], // 当前评分体系
			selectedFactorNode: {}, // 当前选中评分项
			selectedSupplierName: "", // 当前选中供应商名称
			selectedSupplier: {}, // 当前选中供应商对象
			expertInfo: {}, // 专家信息
			ratingStateMap: {}, // 评分项状态与原因
			fileInfo: {}, // 文件信息
			isResponseVisible: false, // 是否显示响应文件
			isProcurementVisible: false, // 是否显示采购文件
			isDoubleView: false, // 是否对比显示
			
			entDocResponsePage: {}, // 响应文件页码信息
			
			factorDetailList: [], // 评分项详情
			factorsPageMap: {}, // 评分项页码信息
			
			entDocProcurementPage: {}, // 采购文件页码信息
			pageProcurement:[], // 采购文件的评分项
			attachmentsList: [], // 文件列表
			
			supplierFactorPageMap: {}, // 当前供应商评分项页码
			responsePdfUrl: null, // 响应文件PDF路径
			procurementPdfUrl: null, // 采购文件PDF路径

			// 按钮状态管理
			activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'

			// PDF渲染状态管理
			responsePdfRendered: false, // 响应文件PDF是否渲染完成
			procurementPdfRendered: false, // 采购文件PDF是否渲染完成

			helpImageList: ["/evalution/help.jpg"], // 步骤图片
			factorKeyMap: { // 评分项与后端字段映射
				"特定资格要求": "zgzs",
				"响应内容": "jsplb",
				"采购需求": "jsplb",
				"供货期限": "ghqx",
				"投标报价": "tbbj"
			},
			checkResult: {}, // 系统初验结果
			checkResultLabel: { // 系统初验结果名称
				"符合《中华人民共和国政府采购法》第二十二条规定": "系统初验通过",
				"特定资格要求": "系统初验通过",
				"信用查询": "系统初验通过",
				"响应人名称": "系统初验通过",
				"响应内容": "系统初验通过",
				"采购需求": "系统初验通过",
				"供货期限": "系统初验通过",
				"投标报价": "系统初验通过"
			},

			// 悬停状态管理
			hoveredFactorNode: null, // 悬停时的评分项
			tooltipTimer: null, // 悬浮框显示定时器
		};
	},

	methods: {
		// ========== 评分相关 ==========
		/**
		 * 系统初验结果判断
		 * @param {string} factorName 评分项名称
		 * @returns {string} 1-通过 0-未通过
		 */
		getCheckResultState(factorName) {
			if (!this.checkResult || Object.keys(this.checkResult).length === 0) return ""; // 如果没有系统初验结果，则返回空
			let state = "1";
			const key = this.factorKeyMap[factorName];
			if (key) {
				state = this.checkResult[key];
				if (factorName === "投标报价" && state === "1") {
					state = this.checkResult["mxbjb"];
				}
			}
			if (state === "0") {
				this.checkResultLabel[factorName] = "系统初验未通过";
			} else {
				state = "1";
				this.checkResultLabel[factorName] = "系统初验通过";
			}
			return state;
		},

		/**
		 * 校验所有评分项是否填写完整
		 * @returns {boolean} 是否全部填写
		 */
		validateAllRatings() {
			for (const item of this.scoringSystem.uitems) {
				const state = this.ratingStateMap[item.entMethodItemId].state;
				const reason = this.ratingStateMap[item.entMethodItemId].reason;
				// 评分结果未填写
				if (state === null || state === '') {
					// this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);
					return true;
				}
				// 不通过但未填写原因 - 将评审项设置为空，然后继续执行后续流程
				if (state === "0" && (!reason || reason.trim() === '')) {
					// 将此评审项设置为空（未评审状态）
					this.ratingStateMap[item.entMethodItemId].state = null;
					this.ratingStateMap[item.entMethodItemId].reason = "";
					console.log(`${item.itemName}评审不通过但未填写备注，已将该评审项设置为空`);
				}
			}
			return true;
		},

		// 生成保存数据
		generateSaveData() {
			const ratingCopy = JSON.parse(JSON.stringify(this.ratingStateMap)); // 评分项状态
			const data = []; // 保存数据
			for (const item of this.scoringSystem.uitems) { // 遍历评分项
				const itemId = item.entMethodItemId; // 评分项ID
				const evaluationResult = ratingCopy[itemId].state; // 评分项状态
				// if (evaluationResult === null || evaluationResult === "") continue; // 如果评分项状态为空，则跳过
				// 注意：不通过原因的校验已经在validateAllRatings中处理，这里只需要构建数据
				const evaluationRemark = ratingCopy[itemId].reason || ""; // 评分项备注
				data.push({
					scoringMethodUitemId: itemId, // 评分项ID
					expertResultId: this.expertInfo.resultId, // 专家ID
					entId: this.selectedSupplier.bidderId, // 供应商ID
					evaluationResult, // 评分项状态
					evaluationRemark // 评分项备注
				});
			}
			return data; // 返回保存数据
		},

		/**
		 * 临时保存评分结果
		 * @returns {Promise}
		 */
		saveRatingTemp() {
			// 先校验所有评分项是否填写完整
			if (!this.validateAllRatings()) {
				return Promise.resolve({ code: 0, success: false }); // 校验失败
			}

			const data = this.generateSaveData(); // 生成保存数据
			if (data.length > 0) {
				return scoringFactors(data).then(response => {
					if (response.code === 200) {
						this.$message.success("保存成功");
						return { code: 200, success: true };
					} else {
						this.$message.warning(response.msg);
						return { code: response.code, success: false };
					}
				}).catch((error) => {
					this.$message.error("保存失败");
					return { code: 0, success: false };
				});
			} else {
				return Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功
			}
		},

		/**
		 * 提交评分结果
		 */
		submitRating() {
			this.saveRatingTemp().then((saveResult) => {
				// 检查保存结果，如果校验失败则不继续提交
				if (!saveResult || saveResult.success === false) {
					return; // 校验失败，不继续提交流程
				}
				this.checkAndSubmitReview();
			});
		},

		// 检查并提交评审汇总
		checkAndSubmitReview() {
				const data = {
					projectId: this.$route.query.projectId,
					expertResultId: this.expertInfo.resultId,
					scoringMethodItemId: this.$route.query.scoringMethodItemId,
				};
				checkReviewSummary(data).then((response) => {
					if (response.code === 200) {
						this.updateExpertScoreStatus(); // 修改专家进度状态
						this.$emit("send", "two"); // 跳转至二次报价
					} else {
						this.$message.warning(response.msg);
					}
			});
		},

		// 修改专家进度状态
		updateExpertScoreStatus() {
			const status = {
				evalExpertScoreInfoId: JSON.parse(localStorage.getItem("evalExpertScoreInfo")).evalExpertScoreInfoId,
				evalState: 1,
			};
			editEvalExpertScoreInfo(status).then((res) => {
				if (res.code === 200) {
					this.$message.success("提交成功");
				}
			});
		},

		// ========== 初始化相关 ==========
		/**
		 * 初始化页面数据
		 */
		initPage() {
			this.initExpertInfo();// 初始化专家信息
			this.initEntDocResponsePage(); // 初始化响应文件页码信息
			this.initEntDocProcurementPage(); // 初始化采购文件页码信息
			this.loadSupplierOptions();// 加载供应商下拉选项
			this.loadScoringSystem(); // 加载评分体系
			this.loadFiles(); // 加载文件信息
		},
		// 初始化专家信息
		initExpertInfo() {
			try {
				const expertInfoStr = localStorage.getItem("expertInfo");
				if (expertInfoStr) {
					this.expertInfo = JSON.parse(expertInfoStr);
					console.log("专家信息已初始化", this.expertInfo);
				} else {
					console.warn("localStorage中未找到expertInfo");
				}
			} catch (error) {
				console.error("初始化专家信息失败:", error);
			}
		},
		// 初始化响应文件页码信息
		initEntDocResponsePage() {
			this.entDocResponsePage = JSON.parse(localStorage.getItem("entDocResponsePage"));
		},
		// 初始化采购文件页码信息
		initEntDocProcurementPage() {
			this.entDocProcurementPage = JSON.parse(localStorage.getItem("entDocProcurementPage"));
		},
		// 加载供应商下拉选项
		loadSupplierOptions() {
			supplierInfo({ projectId: this.$route.query.projectId }).then((response) => {
				if (response.code === 200) {
					this.supplierOptions = response.rows.filter(item => item.isAbandonedBid === 0); // 过滤掉被放弃的投标
					console.log(this.supplierOptions);
				} else {
					this.$message.warning(response.msg);
				}
			});
		},
		// 加载评分体系及初始化评分项状态
		loadScoringSystem() {
			approvalProcess(this.$route.query.projectId, this.expertInfo.resultId).then((response) => {
				if (response.code === 200) {
					// 文件列表
					this.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == "0");

					this.scoringSystem = response.data.scoringMethodUinfo.scoringMethodItems.find(
						item => item.scoringMethodItemId == this.$route.query.scoringMethodItemId
					); // 获取当前评分项
					localStorage.setItem(
						"evalProjectEvaluationProcess",
						JSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)
					); // 保存评分体系
					console.log(this.scoringSystem);
					this.initRatingStateMapBySystem(); // 初始化评分项状态
				} else {
					this.$message.warning(response.msg);
				}
			});
		},
		// 初始化评分项状态（根据评分体系）
		initRatingStateMapBySystem() {
			this.ratingStateMap = this.scoringSystem.uitems.reduce((acc, item) => {
				acc[item.entMethodItemId] = { state: null, reason: "" };
				return acc;
			}, {}); // 初始化评分项状态
		},
		// 加载文件信息
		loadFiles() {
			filesById(this.$route.query.projectId).then((response) => {
				if (response.code === 200) {
					this.fileInfo = response.data; // 文件信息
					// 🎯 需求一：不自动设置采购文件URL，只在用户点击时才设置
					// if (this.fileInfo.tenderNoticeFilePath) {
					// 	this.procurementPdfUrl = this.fileInfo.tenderNoticeFilePath; // 采购文件
					// }
					// 不自动设置响应文件URL，只在需要时才设置
					// if (this.fileInfo.file) {
					// 	this.responsePdfUrl = this.fileInfo.file[0]; // 响应文件
					// }
				} else {
					this.$message.warning(response.msg);
				}
			});
		},

		// ========== 供应商相关 ==========
		/**
		 * 供应商切换时处理
		 * @param {string} supplierName 供应商名称
		 */
		handleSupplierChange(supplierName) {
			// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换供应商
			if (this.isAnyPdfRendering()) {
				this.$message.warning("PDF文件正在渲染中，请稍候再切换供应商");
				// 恢复之前的选择
				this.$nextTick(() => {
					this.selectedSupplierName = this.selectedSupplier.bidderName || "";
				});
				return;
			}

			// 🎯 新需求：在切换供应商前，检查并清空不通过但未填写原因的评分项
			this.clearInvalidFailedRatings();

			this.isResponseVisible = true
			if (Object.keys(this.selectedSupplier).length !== 0) {
				this.saveRatingTemp();
			}
			this.selectedSupplier = this.supplierOptions.find(item => item.bidderName === supplierName) || {}; // 获取当前供应商
			this.supplierFactorPageMap = this.factorsPageMap[this.selectedSupplier.bidderId] || {}; // 获取当前供应商评分项页码
			this.loadSupplierFactorDetail(supplierName); // 加载当前供应商评分项详情
			this.loadSupplierCheckResult(); // 加载当前供应商系统初验结果
			this.showResponseFile(); // 显示响应文件
		},
		// 加载当前供应商评分项详情
		loadSupplierFactorDetail(bidderName) {
			this.clearRatingStateMap();
			
			const detailData = {
				expertResultId: this.expertInfo.resultId,
				projectId: this.$route.query.projectId,
				scoringMethodItemId: this.$route.query.scoringMethodItemId,
			};
			getDetailByPsxx(detailData).then((response) => {
				if (response.code === 200) {
					this.factorDetailList = response.data;
					const factor = this.factorDetailList.find(item => item.bidderName === bidderName)?.evalExpertEvaluationDetails;
					if (factor) {
						this.setRatingStateMapByFactor(factor);
					} else {
						this.clearRatingStateMap();
					}
				} else {
					this.$message.warning(response.msg);
				}
			});
		},
		// 根据评分详情设置评分项状态
		setRatingStateMapByFactor(factor) {
			factor.forEach(item => {
				this.ratingStateMap[item.scoringMethodUitemId].reason = item.evaluationRemark;
				this.ratingStateMap[item.scoringMethodUitemId].state = item.evaluationResult;
			});
		},
		// 加载当前供应商系统初验结果
		loadSupplierCheckResult() {
			const reviewData = {
				projectId: this.$route.query.projectId,
				entId: this.selectedSupplier.bidderId,
			};
			resDocReviewFactorsDecision(reviewData).then((res) => {
				if (res.code === 200) {
					this.checkResult = res.data;
				}
			});
		},
		// 初始化评分项状态（清空）
		clearRatingStateMap() {
			Object.keys(this.ratingStateMap).forEach(key => {
				this.ratingStateMap[key].state = null;
				this.ratingStateMap[key].reason = "";
			});
		},

		/**
		 * 检查并清空不通过但未填写原因的评分项
		 * 如果用户点击了不通过并且没有填写原因，则将该评分项置空
		 */
		clearInvalidFailedRatings() {
			for (const item of this.scoringSystem.uitems) {
				const itemId = item.entMethodItemId;
				const state = this.ratingStateMap[itemId].state;
				const reason = this.ratingStateMap[itemId].reason;
				
				// 如果评分为不通过(0)但未填写原因，则将该评分项置空
				if (state === "0" && (!reason || reason.trim() === '')) {
					this.ratingStateMap[itemId].state = null;
					this.ratingStateMap[itemId].reason = "";
					console.log(`${item.itemName}评分不通过但未填写原因，已将该评分项置空`);
				}
			}
		},

		// ========== 文件相关 ==========
		/**
		 * 显示响应文件
		 */
		showResponseFile() {
			if (Object.keys(this.selectedSupplier).length === 0) {
				this.$message.warning("请选择供应商");
			} else {
				// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换
				if (this.isAnyPdfRendering()) {
					this.$message.warning("PDF文件正在渲染中，请稍候再切换");
					return;
				}

				// 🎯 新需求：在切换到响应文件前，检查并清空不通过但未填写原因的评分项
				this.clearInvalidFailedRatings();

				this.activeButton = 'response'; // 设置当前激活按钮
				this.isDoubleView = false; // 关闭对比
				this.isProcurementVisible = false; // 关闭采购文件
				this.isResponseVisible = true; // 显示响应文件

				// 🎯 只在用户点击时才设置响应文件URL，开始渲染
				this.responsePdfUrl = this.fileInfo.file[this.selectedSupplier.bidderId]; // 响应文件

				// 右侧评分项显示为响应文件的评分项
				if (Object.keys(this.selectedSupplier).length !== 0) {
					this.saveRatingTemp();
				}
				this.selectedSupplier = this.supplierOptions.find(item => item.bidderName === this.selectedSupplier.bidderName) || {}; // 获取当前供应商
				this.supplierFactorPageMap = this.factorsPageMap[this.selectedSupplier.bidderId] || {}; // 获取当前供应商评分项页码
				this.loadSupplierFactorDetail(this.selectedSupplier.bidderName); // 加载当前供应商评分项详情
				this.loadSupplierCheckResult(); // 加载当前供应商系统初验结果
			}
		},
		/**
		 * 文件对比显示
		 */
		showFileContrast() {
			if (Object.keys(this.selectedSupplier).length === 0) {
				this.$message.warning("请选择供应商");
			} else {
				// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换
				if (this.isAnyPdfRendering()) {
					this.$message.warning("PDF文件正在渲染中，请稍候再切换");
					return;
				}

				// 🎯 新需求：在切换到对比模式前，检查并清空不通过但未填写原因的评分项
				this.clearInvalidFailedRatings();

				this.activeButton = 'contrast'; // 设置当前激活按钮
				this.isDoubleView = true;
				this.isProcurementVisible = true;
				this.isResponseVisible = true;

				// 🎯 只在用户点击对比时才设置文件URL，开始渲染
				this.responsePdfUrl = this.fileInfo.file[this.selectedSupplier.bidderId]; // 响应文件
				if (this.fileInfo.tenderNoticeFilePath) {
					this.procurementPdfUrl = this.fileInfo.tenderNoticeFilePath; // 采购文件
				}
			}
		},
		/**
		 * 查看采购文件
		 */
		showProcurementFile() {
			// 🎯 需求二：检查是否有PDF正在渲染，如果有则禁止切换
			if (this.isAnyPdfRendering()) {
				this.$message.warning("PDF文件正在渲染中，请稍候再切换");
				return;
			}

			// 🎯 新需求：在切换到采购文件前，检查并清空不通过但未填写原因的评分项
			this.clearInvalidFailedRatings();

			this.activeButton = 'procurement'; // 设置当前激活按钮
			this.isDoubleView = false;
			this.isResponseVisible = false;
			this.isProcurementVisible = true;

			// 🎯 需求一：只在用户点击时才设置采购文件URL，开始渲染
			if (this.fileInfo.tenderNoticeFilePath) {
				this.procurementPdfUrl = this.fileInfo.tenderNoticeFilePath; // 采购文件
			}

			// 右侧评分项显示为采购文件的评分项
			let pageProcurementArr = [];
			for (let item in this.entDocProcurementPage){
				pageProcurementArr.push({
					itemName: item,
					jumpToPage: this.entDocProcurementPage[item]
				})
			}

			console.log(this.scoringSystem.uitems);
			console.log(pageProcurementArr)
			this.pageProcurement = [];
			for (let i = 0; i < this.scoringSystem.uitems.length;i++){
				for (let j = 0; j < pageProcurementArr.length;j++){
					if (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){
						this.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});
					}
				}
			}
			console.log(this.pageProcurement)
		},

		// ========== 页面跳转相关 ==========
		/**
		 * 跳转到评分项对应页码
		 * @param {Object} factorItem 评分项对象
		 */
		jumpToFactorPage(factorItem) {
			// 检查PDF是否渲染完成
			if (!this.canJumpToPage()) {
				this.$message.warning("PDF页面正在渲染中，请稍候再试");
				return;
			}

			this.selectedFactorNode = factorItem; // 设置当前选中因子

			// 如果只显示采购文件，使用采购文件页码信息
			if (this.isProcurementVisible && !this.isResponseVisible) {
				if (!this.procurementPdfRendered) {
					this.$message.warning("采购文件正在渲染中，请稍候再试");
					return;
				}
				if (factorItem.jumpToPage) {
					this.$refs.procurement.skipPage(factorItem.jumpToPage); // 采购文件跳页
				} else if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {
					this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页
				}
				return;
			}

			// 如果显示响应文件或对比模式，需要选择供应商
			if (!this.supplierFactorPageMap || Object.keys(this.supplierFactorPageMap).length === 0) {
				this.$message.warning("请先选择供应商"); // 未选供应商提示
				return;
			}

			// 跳转到响应文件对应页码
			if (this.isResponseVisible && this.$refs.response) {
				if (!this.responsePdfRendered) {
					this.$message.warning("响应文件正在渲染中，请稍候再试");
					return;
				}
				this.$refs.response.skipPage(this.supplierFactorPageMap[this.selectedFactorNode.itemName]); // 响应文件跳页
			}

			// 跳转到采购文件对应页码
			if (this.isProcurementVisible && this.$refs.procurement) {
				if (!this.procurementPdfRendered) {
					this.$message.warning("采购文件正在渲染中，请稍候再试");
					return;
				}
				// 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码
				if (this.entDocProcurementPage && this.entDocProcurementPage[factorItem.itemName]) {
					this.$refs.procurement.skipPage(this.entDocProcurementPage[factorItem.itemName]); // 采购文件跳页
				} else {
					// 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件
					// 这样可以避免采购文件和响应文件显示不同的内容造成混淆
				}
			}
		},

		/**
		 * 检查是否可以跳转页面
		 * @returns {boolean} 是否可以跳转
		 */
		canJumpToPage() {
			// 如果只显示采购文件
			if (this.isProcurementVisible && !this.isResponseVisible) {
				return this.procurementPdfRendered;
			}
			// 如果只显示响应文件
			if (this.isResponseVisible && !this.isProcurementVisible) {
				return this.responsePdfRendered;
			}
			// 如果对比模式（两个都显示）
			if (this.isResponseVisible && this.isProcurementVisible) {
				return this.responsePdfRendered && this.procurementPdfRendered;
			}
			return false;
		},

		/**
		 * 🎯 需求二：检查是否有任何PDF正在渲染
		 * @returns {boolean} 是否有PDF正在渲染
		 */
		isAnyPdfRendering() {
			// 检查当前显示的PDF是否正在渲染
			if (this.isProcurementVisible && this.procurementPdfUrl && !this.procurementPdfRendered) {
				return true;
			}
			if (this.isResponseVisible && this.responsePdfUrl && !this.responsePdfRendered) {
				return true;
			}
			return false;
		},

		/**
		 * 处理PDF渲染状态变化
		 * @param {boolean} isRendered 是否渲染完成
		 * @param {string} pdfType PDF类型：'response' 或 'procurement'
		 */
		handlePdfRenderStatusChange(isRendered, pdfType) {
			if (pdfType === 'response') {
				this.responsePdfRendered = isRendered;
			} else if (pdfType === 'procurement') {
				this.procurementPdfRendered = isRendered;
			}
			
			if (isRendered) {
				console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);
			}
		},
		/**
		 * 跳转到二次报价
		 */
		goToSecondOffer() {
			const query = {
				projectId: this.$route.query.projectId,
				zjhm: this.$route.query.zjhm,
				scoringMethodItemId: JSON.parse(localStorage.getItem("tenderOfferScoringMethodItems")),
			};
			this.$router.push({ path: "/secondOffer", query });
		},
		/**
		 * 跳转到询标
		 */
		goToBidInquiry() {
			const query = {
				projectId: this.$route.query.projectId,
				zjhm: this.$route.query.zjhm,
				scoringMethodItemId: JSON.parse(localStorage.getItem("tenderOfferScoringMethodItems")),
			};
			this.$router.push({ path: "/bidInquiry", query });
		},
		/**
		 * 获取因素对应页码
		 */
		loadFactorsPageMap() {
			this.factorsPageMap = JSON.parse(localStorage.getItem("entDocResponsePage"));
		},

		/**
		 * 下载文件
		 * @param {Object} item - 文件对象
		 */
		downloadFile(item) {
			this.$download.zip(item.filePath, item.fileName);
		},

		// ========== 悬停相关 ==========
		/**
		 * 显示评分项悬浮框
		 * @param {Object} factorItem 评分项对象
		 */
		showFactorTooltip(factorItem) {
			if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示

			// 清除之前的定时器
			if (this.tooltipTimer) {
				clearTimeout(this.tooltipTimer);
			}

			// 延迟显示悬浮框，避免快速移动时频繁显示
			this.tooltipTimer = setTimeout(() => {
				this.hoveredFactorNode = factorItem;
			}, 300); // 300ms延迟
		},

		/**
		 * 隐藏评分项悬浮框
		 */
		hideFactorTooltip() {
			// 清除定时器
			if (this.tooltipTimer) {
				clearTimeout(this.tooltipTimer);
				this.tooltipTimer = null;
			}

			// 延迟隐藏，给用户时间移动到悬浮框上
			setTimeout(() => {
				this.hoveredFactorNode = null;
			}, 100);
		},

		/**
		 * 清除悬浮框定时器（当鼠标移动到悬浮框上时）
		 */
		clearTooltipTimer() {
			if (this.tooltipTimer) {
				clearTimeout(this.tooltipTimer);
				this.tooltipTimer = null;
			}
		}
	
},

	mounted() {
		this.initPage();
		this.loadFactorsPageMap();
	},

	beforeDestroy() {
		// 清理定时器
		if (this.tooltipTimer) {
			clearTimeout(this.tooltipTimer);
			this.tooltipTimer = null;
		}
	},
};
</script>

<style lang="scss" scoped>
.main-container-one {
	min-height: 57vh;
	display: flex;
}
.left-panel {
	min-height: 57vh;
	width: 79%;
}
.header-bar {
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 2px solid #176ADB;
	padding: 15px 20px;
}
.header-title {
	display: flex;
	height: 36px;
	font-weight: 700;
	font-size: 24px;
	color: #333;
}
.header-steps {
	display: grid;
	justify-items: center;
	position: relative;
	bottom: -30px;
}
.steps-tip {
	font-size: 12px;
}
.steps-img {
	width: 80px;
	height: 30px;
	margin-right: 20px;
}
.header-btns {
	text-align: right;
}
.header-btns-group {
	margin-top: 20px;
}
.item-button.main {
	background-color: #176ADB;
	color: #fff;
	border: 1px solid #176ADB;
}
.pdf-container {
	display: flex;
	justify-content: center;
	height: 100%;
	min-height: 600px;
}
.pdf-view {
	width: 49%;
}
.border-right {
	border-right: 1px solid #176ADB;
}
.border-left {
	border-left: 1px solid #176ADB;
}
.divider {
	min-height: 57vh;
	width: 1%;
	background-color: #F5F5F5;
}
.right-panel {
	min-height: 57vh;
	width: 20%;
}
.right-header {
	display: flex;
	justify-content: center;
	align-items: center;
	border-bottom: 2px solid #176ADB;
	padding: 15px 20px;
}
.right-content {
	padding: 15px 20px;
}
.factor-item {
	margin-bottom: 10px;
}
.factors {
	display: flex;
	justify-content: flex-start;
	flex-wrap: wrap;
	align-items: center;
	margin-bottom: 10px;
}
.factor-title {
	cursor: pointer;
	font-family: SourceHanSansSC-Bold;
	font-weight: 700;
	font-size: 16px;
	color: #333;
	letter-spacing: 0;
	width: auto;
	text-align: left;
	transition: all 0.3s ease;
	padding: 4px 8px;
	border-radius: 4px;

	&:hover {
		background-color: #f0f8ff;
		color: #176ADB;
		transform: translateX(2px);
	}
}
.factor-radio-group {
	display: flex;
	width: 100%;
	justify-content: flex-end;
}
.factor-divider {
	height: 1px;
	background-color: #DCDFE6;
	margin-top: 10px;
}
.right-btns {
	display: flex;
	margin: 32px 0;
	justify-content: space-evenly;
}
.review-content {
	text-align: left;
	font-size: 14px;
}
.review-title {
	font-family: SourceHanSansSC-Bold;
	font-weight: 700;
	font-size: 15px;
	color: #176ADB;
	letter-spacing: 0;
}
.review-html {
	padding: 6px 30px;
}
.item-button {
	border: 1px solid #979797;
	width: 150px;
	height: 36px;
	margin: 0 10px;
	font-weight: 700;
	font-size: 17px;
	border-radius: 6px;
	color: #333;
	&:hover {
		color: #333;
	}
}
.qualification-blue-btn {
	background-color: #fff !important;
	color: #333 !important;
	border: 1px solid #979797 !important;
}
.qualification-blue-btn-active {
	background-color: #176ADB !important;
	color: #fff !important;
	border: 1px solid #176ADB !important;
}
.item-button-little {
	width: 124px;
	height: 36px;
	font-weight: 700;
	font-size: 18px;
	color: #fff;
	background-color: #176ADB;
	&:hover {
		color: #fff;
	}
}
.text {
	::v-deep .el-textarea__inner {
		background-color: #f5f5f5;
		border-radius: 0;
		border: 1px solid #f5f5f5;
	}
}

.fileList {
	display: flex;
	align-items: center;
	gap: 20px;
	flex: 1;
	flex-wrap: wrap;
	.fileItem {
		transition: all 0.3s ease;
		&:hover {
			transform: translateY(-2px);
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		}

		::v-deep .el-card__body {
			padding: 0;
		}
	}
}

// PDF渲染状态提示样式
.render-status-tip {
	display: flex;
	align-items: center;
	padding: 10px 15px;
	margin-bottom: 15px;
	border-radius: 4px;
	background-color: #fff7e6;
	border: 1px solid #ffd591;
	color: #d48806;
	font-size: 14px;
	
	i {
		margin-right: 8px;
		font-size: 16px;
	}
	
	&.success {
		background-color: #f6ffed;
		border-color: #b7eb8f;
		color: #52c41a;
	}
}

// 禁用状态的评分项标题样式
.factor-title.disabled {
	color: #999 !important;
	cursor: not-allowed !important;
	opacity: 0.6;

	&:hover {
		color: #999 !important;
	}
}

// 悬浮框样式
.factor-tooltip {
	position: absolute;
	right: 100%; /* 显示在父元素左侧 */
	top: 0;
	margin-right: 10px; /* 与评分项的间距 */
	background: #fff;
	border: 1px solid #e4e7ed;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	width: 400px;
	max-height: 300px;
	overflow: hidden;
	z-index: 9999;

	.tooltip-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 12px 16px;
		background-color: #f5f7fa;
		border-bottom: 1px solid #e4e7ed;

		.tooltip-title {
			font-weight: 600;
			font-size: 14px;
			color: #176ADB;
		}

		.tooltip-close {
			cursor: pointer;
			color: #909399;
			font-size: 14px;

			&:hover {
				color: #176ADB;
			}
		}
	}

	.tooltip-content {
		padding: 16px;
		font-size: 14px;
		line-height: 1.6;
		color: #333;
		max-height: 240px;
		overflow-y: auto;

		// 美化滚动条
		&::-webkit-scrollbar {
			width: 6px;
		}

		&::-webkit-scrollbar-track {
			background: #f1f1f1;
			border-radius: 3px;
		}

		&::-webkit-scrollbar-thumb {
			background: #c1c1c1;
			border-radius: 3px;

			&:hover {
				background: #a8a8a8;
			}
		}
	}
}

// 评分项容器相对定位
.factor-item {
	position: relative;
}

// 🎯 需求二：PDF渲染期间禁用状态样式
.item-button:disabled {
	opacity: 0.6 !important;
	cursor: not-allowed !important;
	background-color: #f5f5f5 !important;
	color: #c0c4cc !important;
	border-color: #e4e7ed !important;
}

.el-select.is-disabled .el-input__inner {
	background-color: #f5f5f5;
	border-color: #e4e7ed;
	color: #c0c4cc;
	cursor: not-allowed;
}
.fileListInner{
	display: flex;
	gap: 20px;
	flex-wrap: wrap;
}
</style>
