# 页面标题
VUE_APP_TITLE = 限额以下交易平台

# 生产环境配置
ENV = 'development'

# 若依管理系统/生产环境
VUE_APP_BASE_API = 'http://localhost:8080'

# VUE_APP_BASE_API = 'http://************:8080'
# 明文

# VUE_APP_BASE_API = 'http://************:8080'
# 冠海

# VUE_APP_BASE_API = 'http://************:8080'
# 测试

#websocket连接地址
VUE_APP_WEBSOCKET_API = 'ws://localhost:8080'
#VUE_APP_WEBSOCKET_API = 'ws://************:8080'
#VUE_APP_WEBSOCKET_API = 'ws://************:8080'

#开放签地址
VUE_APP_KAIFANFQIAN_API = 'http://localhost:8081'
#VUE_APP_KAIFANFQIAN_API = 'http://************:8081'
# VUE_APP_KAIFANFQIAN_API = 'http://************:8081'

#政采协会网站地址
VUE_APP_ZCXH_URL='http://localhost/login'
#VUE_APP_ZCXH_URL='http://localhost:4000/#/login'

VUE_CLI_BABEL_TRANSPILE_MODULES = true



