package com.ruoyi.busi.mapper;

import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.busi.domain.BusiAttachment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 附件Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Mapper
public interface BusiAttachmentMapper extends BaseMapper<BusiAttachment> {
    List<BusiAttachment> selectByBusiIdAndType(@Param("busiId") Long busiId, @Param("fileType") String fileType);
    int deleteByBusiId(Long busiId);
    int deleteByBusiIdAndType(@Param("busiId") Long busiId, @Param("fileType") String fileType);
}