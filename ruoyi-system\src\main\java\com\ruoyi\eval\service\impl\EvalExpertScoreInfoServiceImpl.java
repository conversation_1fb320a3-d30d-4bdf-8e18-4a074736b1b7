package com.ruoyi.eval.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.busi.domain.BusiExtractExpertResult;
import com.ruoyi.busi.service.IBusiBidderInfoService;
import com.ruoyi.busi.service.IBusiExtractExpertResultService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.eval.domain.EvalExpertScoreInfo;
import com.ruoyi.eval.mapper.EvalExpertScoreInfoMapper;
import com.ruoyi.eval.service.IEvalAgainQuoteService;
import com.ruoyi.eval.service.IEvalExpertEvaluationDetailService;
import com.ruoyi.eval.service.IEvalExpertScoreInfoService;
import com.ruoyi.eval.service.IEvalProjectEvaluationProcessService;
import com.ruoyi.scoring.service.IScoringMethodItemService;
import com.ruoyi.scoring.service.IScoringMethodUinfoService;
import com.ruoyi.scoring.service.IScoringMethodUitemService;
import com.ruoyi.eval.websocket.WebSocketUsers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 专家打分详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-08-21
 */
@Service
public class EvalExpertScoreInfoServiceImpl extends ServiceImpl<EvalExpertScoreInfoMapper, EvalExpertScoreInfo> implements IEvalExpertScoreInfoService {
    @Autowired
    private IBusiBidderInfoService busiBidderInfoService;
    @Autowired
    private IScoringMethodItemService scoringMethodItemService;
    @Autowired
    private IScoringMethodUitemService scoringMethodUitemService;
    @Autowired
    private IEvalExpertEvaluationDetailService evalExpertEvaluationDetailService;
    @Autowired
    private IBusiExtractExpertResultService busiExtractExpertResultService;
    @Autowired
    private IScoringMethodUinfoService scoringMethodUinfoService;
    @Autowired
    private IEvalProjectEvaluationProcessService evalProjectEvaluationProcessService;
    @Autowired
    private IEvalAgainQuoteService evalAgainQuoteService;
    /**
     * 查询专家打分详情列表
     *
     * @param evalExpertScoreInfo 专家打分详情
     * @return 专家打分详情
     */
    @Override
    public List<EvalExpertScoreInfo> selectList(EvalExpertScoreInfo evalExpertScoreInfo) {
        QueryWrapper<EvalExpertScoreInfo> evalExpertEvaluationInfoQueryWrapper = new QueryWrapper<>();
        evalExpertEvaluationInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertScoreInfo.getEvalExpertScoreInfoId()),
                "eval_expert_score_info_id",evalExpertScoreInfo.getEvalExpertScoreInfoId());
        evalExpertEvaluationInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertScoreInfo.getProjectEvaluationId()),
                "project_evaluation_id",evalExpertScoreInfo.getProjectEvaluationId());
        evalExpertEvaluationInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertScoreInfo.getExpertResultId()),
                "expert_result_id",evalExpertScoreInfo.getExpertResultId());
        evalExpertEvaluationInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(evalExpertScoreInfo.getScoringMethodItemId()),
                "scoring_method_item_id",evalExpertScoreInfo.getScoringMethodItemId());
        return list(evalExpertEvaluationInfoQueryWrapper);
    }

    @Override
    public AjaxResult updateState(Long evalExpertScoreInfoId, Integer evalState) {
        EvalExpertScoreInfo info = getById(evalExpertScoreInfoId);
        info.setEvalState(evalState);
        if(updateById(info)){
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    @Override
    public AjaxResult addEvalExpertScoreInfo(EvalExpertScoreInfo evalExpertScoreInfo) {
        List<EvalExpertScoreInfo> list = list(new QueryWrapper<EvalExpertScoreInfo>()
                .eq("expert_result_id", evalExpertScoreInfo.getExpertResultId())
                .eq("project_evaluation_id", evalExpertScoreInfo.getProjectEvaluationId())
                .eq("scoring_method_item_id", evalExpertScoreInfo.getScoringMethodItemId())
        );

        if (list.size()>1){
            return AjaxResult.error("当前专家已在该评审节点评审过了");
        }else {
            BusiExtractExpertResult extractExpertResultServiceById = busiExtractExpertResultService.getById(evalExpertScoreInfo.getExpertResultId());
            evalExpertScoreInfo.getParams().put("opUser",extractExpertResultServiceById.getExpertName());
            saveOrUpdate(evalExpertScoreInfo);
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult getEvalExpertScoreInfo(EvalExpertScoreInfo evalExpertScoreInfo) {
        //this.getOne()
        List<EvalExpertScoreInfo> list = list(new QueryWrapper<EvalExpertScoreInfo>()
                .eq("expert_result_id", evalExpertScoreInfo.getExpertResultId())
                .eq("project_evaluation_id", evalExpertScoreInfo.getProjectEvaluationId())
                .eq("scoring_method_item_id", evalExpertScoreInfo.getScoringMethodItemId())
        );
        if (list.size()>1){
            return AjaxResult.error("当前专家已在该评审节点评审过了");
        }else if(list.size()==1){
            return AjaxResult.success(list.get(0));
        }else{
            evalExpertScoreInfo.setEvalExpertScoreInfoId(IdUtils.longUUID());
            evalExpertScoreInfo.setEvalState(0);
            BusiExtractExpertResult extractExpertResultServiceById = busiExtractExpertResultService.getById(evalExpertScoreInfo.getExpertResultId());
            evalExpertScoreInfo.getParams().put("opUser",extractExpertResultServiceById.getExpertName());
            save(evalExpertScoreInfo);
            return AjaxResult.success(evalExpertScoreInfo);
        }
    }

    @Override
    public AjaxResult reEvaluation(EvalExpertScoreInfo evalExpertScoreInfo) {
        List<EvalExpertScoreInfo> list = list(new QueryWrapper<EvalExpertScoreInfo>()
                .eq("project_evaluation_id", evalExpertScoreInfo.getProjectEvaluationId())
                .eq("scoring_method_item_id", evalExpertScoreInfo.getScoringMethodItemId())
        );
        if (list!=null && !list.isEmpty()) {
            BusiExtractExpertResult extractExpertResultServiceById = busiExtractExpertResultService.getById(evalExpertScoreInfo.getExpertResultId());
            list.forEach(expertScoreInfo -> {
                expertScoreInfo.setEvalState(0);
                expertScoreInfo.getParams().put("opUser", extractExpertResultServiceById.getExpertName());
            });
            saveOrUpdateBatch(list);
            
            // 添加 WebSocket 消息推送逻辑
            // 获取项目ID和专家列表
            Long projectId = evalExpertScoreInfo.getProjectEvaluationId();
            List<BusiExtractExpertResult> expertResults = busiExtractExpertResultService.list(
                new QueryWrapper<BusiExtractExpertResult>()
                        .inSql("apply_id", "select apply_id from busi_extract_expert_apply where project_id="+projectId+" and del_flag=0")
            );

            // 构建详细的消息对象
            JSONObject messageObj = new JSONObject();
            messageObj.put("type", "reEvaluation");
            messageObj.put("projectId", projectId);
            messageObj.put("message", "专家组长发起了重新评审");

            String message = messageObj.toJSONString();

            // 向所有专家推送重新评审消息
            for (BusiExtractExpertResult expertResult : expertResults) {
                String key = expertResult.getResultId() + "_" + projectId + "_1";
                WebSocketUsers.sendMessageToUserByKey(key, message);
            }
        }
        return AjaxResult.success();
    }

}
