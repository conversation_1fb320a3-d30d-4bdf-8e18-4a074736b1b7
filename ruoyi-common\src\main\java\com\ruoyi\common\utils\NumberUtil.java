package com.ruoyi.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class NumberUtil {

    public static String toString(BigDecimal number){
        return toString(number, 2);
    }
    public static String toString(BigDecimal number, int scale) {
        if(number!=null) {
            return String.valueOf(number.setScale(scale, RoundingMode.HALF_UP));
        }
        return "0";
    }

        public static void main(String[] args) {
            double number = 2700;
            StringBuffer buffer = transformation(number);
            System.out.println(buffer);
            String s = dealMoney(String.valueOf(number));
            System.out.println(s);
            int n = 2700;
            String s2 = dealMoney(String.valueOf(n));
            System.out.println(s2);
        }
        private static StringBuffer transformation(Double number) {
            //定义大写Map
            Map<Integer,String> map = new HashMap<>();
            map.put(0,"零");
            map.put(1,"壹");
            map.put(2,"贰");
            map.put(3,"叄");
            map.put(4,"肆");
            map.put(5,"伍");
            map.put(6,"陆");
            map.put(7,"柒");
            map.put(8,"捌");
            map.put(9,"玖");
            ArrayList<String> list = new ArrayList<>();
            char[] chars = String.valueOf(number).toCharArray();
            int whichNumber = 0;
            for (int i = 0; i < chars.length; i++) {
                //判断是否小数点
                if (!String.valueOf(chars[i]).equals(".")){
                    String value = map.get(Integer.valueOf(String.valueOf(chars[i])));
                    list.add(value);
                }else {
                    //当执行了到这里时就知道小数点在多少位了
                    whichNumber = i+1;
                }
            }
            return splicing(list, whichNumber);
        }

        private static StringBuffer splicing(ArrayList<String> list,int whichNumber) {
            //定义每位数对应的范围
            Map<Integer, String> mapValue= new HashMap<>();
            mapValue.put(1,"元");
            mapValue.put(2,"拾");
            mapValue.put(3,"百");
            mapValue.put(4,"千");
            mapValue.put(5,"万");
            mapValue.put(6,"拾");
            mapValue.put(7,"百");
            mapValue.put(8,"千");
            mapValue.put(9,"亿");
            mapValue.put(10,"拾");
            mapValue.put(0,"角");
            mapValue.put(-1,"分");
            mapValue.put(-2,"哩");
            StringBuffer stringBuffer = new StringBuffer();
            for (String s : list) {
                //定义小数点的值,每减一次,map的key也会变小
                whichNumber --;
                if (whichNumber < 0) {
                    stringBuffer = stringBuffer.append(s).append(mapValue.get(whichNumber));
                }else{
                    stringBuffer.append(s).append(mapValue.get(whichNumber));
                }
            }
            return stringBuffer;
        }

    public static String dealMoney(BigDecimal number) {
        return dealMoney(String.valueOf(number));
    }
    public static String dealMoney(String inputStr) {
        StringBuilder resultStrBuld = new StringBuilder();
        String[] split = inputStr.split("\\.");
        String intStr = split[0];
        //@@@@@@@@@@处理整数部分
        try {
            //四位间隔的大单位
            String BigUnit[] = {"亿", "万", "元"};
            //四位之间的小单位
            String smallUnit[] = {"千", "百", "拾", ""};
            String[] upNum = {"零","壹","贰","叁","肆","伍","陆","柒","捌","玖"};
            if (intStr.matches("^[0]+$")) {
                //如果全是输入的 0 返回
                if(split.length==2 && split[1].matches("^[0]+$")){
                    return "零元零角零分";
                }
            }
            //去掉以整数部分为0开头的情况 eg:0000456 return 456
            intStr = cutFrontZeros(intStr);
            //按照四位进行处理进行转换，length是处理的次数
            int length = intStr.length() / 4;
            if ((intStr.length() % 4) > 0) {
                //有余数次数+1
                length++;
            }
            //获取截取的前索引
            int indexS = 0;
            int indexE = 0;
            //处理四位间隔数据
            for (int i = 0; i < length; i++) {
                //取大单位的索引
                int Bindex = 0;
                if (length < 3) {
                    Bindex = i + (3 - length);
                } else {
                    Bindex = i;
                }
                //分割索引
                if (i == 0 && ((intStr.length() % 4) > 0)) {
                    indexE = intStr.length() % 4;
                } else {
                    indexE = indexE + 4;
                }
                String substrNum = intStr.substring(indexS, indexE);
                //处理四位之间数据
                for (int j = 0; j < substrNum.length(); j++) {
                    //判断截取字符串之后是否全是0
                    boolean subStrAllzero = false;
                    //substrNum后面全是0
                    if (substrNum.substring(j, substrNum.length()).matches("^[0]+$")) {
                        subStrAllzero = true;
                    }
                    //判断这一位是否是0
                    boolean thisCharIs = false;
                    if ("0".equals(String.valueOf(substrNum.charAt(j)))) {
                        thisCharIs = true;
                    } else {
                        thisCharIs = false;
                    }
                    //取小单位索引
                    int Sindex = 0;
                    if (substrNum.length() != 4) {
                        Sindex = Sindex + (4 - substrNum.length() + j);
                    } else {
                        Sindex = j;
                    }
                    /*拼接数字对应汉字
                        如果后面全是0拼接单位，并结束循环(需要剔除0000这种情况)
                        否则拼接大写汉字,如果上一位且这一位是零不拼接汉字
                        */
                    if (subStrAllzero) {
                        if (j != 0 || i == (length - 1)) {
                            resultStrBuld.append(BigUnit[Bindex]);
                        }
                        break;
                    } else { //if((!lastCharIs || !thisCharIs))
                        if (resultStrBuld.toString().endsWith("零") && thisCharIs) {

                        } else {
                            resultStrBuld.append(upNum[Integer.parseInt(String.valueOf(substrNum.charAt(j)))]);
                        }
                    }
                    /*
                     * 拼接单位
                     *   如果是最后一位，拼接大单位
                     *   否则拼接小单位
                     *       如果上一位是零则不拼接单位，等非零情况拼接单位
                     * */
                    if ((j == (substrNum.length() - 1))) {
                        resultStrBuld.append(BigUnit[Bindex]);
                    } else {
                        if (!resultStrBuld.toString().endsWith("零")) {
                            resultStrBuld.append(smallUnit[Sindex]);
                        }
                    }
                }
                //重置字符串截取的开始索引。
                indexS = indexE;
            }
            //@@@@@@@@@@处理整数部分 END

            //@@@@@@@@@@处理小数部分
            if (split.length == 2) {
                String point = split[1];
                if (point.length() == 1) {
                    resultStrBuld.append(upNum[Integer.parseInt(String.valueOf(point.charAt(0)))]).append("角").append("零分");
                } else {
                    resultStrBuld.append(upNum[Integer.parseInt(String.valueOf(point.charAt(0)))]).append("角").append(upNum[Integer.parseInt(String.valueOf(point.charAt(1)))]).append("分");
                }
            } else {
                resultStrBuld.append("整");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return "null";
        }
        return resultStrBuld.toString();
    }

    public static String cutFrontZeros(String str) {
        //如果全为整数部分全为0，保留一个0
        if("".equals(str.replaceAll("0",""))){
            return "0";
        }
        String s = str;
        if (str.startsWith("0")) {
            s = cutFrontZeros(str.substring(1, str.length()));
        }
        return s;
    }
}
