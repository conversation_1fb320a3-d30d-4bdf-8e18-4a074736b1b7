package com.ruoyi.base.controller;

import cn.hutool.crypto.digest.MD5;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.sign.Md5Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 企业信息Controller
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Api(tags = "企业信息管理")
@RestController
@RequestMapping("/ent/info")
public class BaseEntInfoController extends BaseController {
    @Autowired
    private IBaseEntInfoService baseEntInfoService;

/**
 * 查询企业信息列表
 */
@PreAuthorize("@ss.hasPermi('ent:info:list')")
@ApiOperation(value = "查询企业信息列表")
@GetMapping("/list")
    public TableDataInfo list(BaseEntInfo baseEntInfo) {
        startPage();
        List<BaseEntInfo> list = baseEntInfoService.selectList(baseEntInfo);
        return getDataTable(list);
    }

    /**
     * 导出企业信息列表
     */
    @PreAuthorize("@ss.hasPermi('ent:info:export')")
    @Log(title = "企业信息", businessType = BusinessType.EXPORT)
    @ApiOperation(value = "导出企业信息列表")
    @PostMapping("/export")
    public void export(HttpServletResponse response, BaseEntInfo baseEntInfo) {
        List<BaseEntInfo> list = baseEntInfoService.selectList(baseEntInfo);
        ExcelUtil<BaseEntInfo> util = new ExcelUtil<BaseEntInfo>(BaseEntInfo. class);
        util.exportExcel(response, list, "企业信息数据");
    }

    /**
     * 获取企业信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('ent:info:query')")
    @ApiOperation(value = "获取企业信息详细信息")
    @ApiImplicitParam(name = "entId", value = "${pkColumn.comment}", required = true, dataType = "${pkColumn.dataType}")
    @GetMapping(value = "/{entId}")
    public AjaxResult getInfo(@PathVariable("entId")Long entId) {
        return success(baseEntInfoService.getById(entId));
    }

    /**
     * 新增企业信息
     */
    @PreAuthorize("@ss.hasPermi('ent:info:add')")
    @Log(title = "企业信息", businessType = BusinessType.INSERT)
    @ApiOperation(value = "新增企业信息")
    @PostMapping
    public AjaxResult add(@RequestBody BaseEntInfo baseEntInfo) {
        baseEntInfo.setCreateBy(getUsername());
        baseEntInfo.setUpdateBy(getUsername());
        baseEntInfo.setSecretKey(MD5.create().digestHex(baseEntInfo.getSecretKey()));
        return toAjax(baseEntInfoService.save(baseEntInfo));
    }

    /**
     * 修改企业信息
     */
    @PreAuthorize("@ss.hasPermi('ent:info:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改企业信息")
    @PutMapping
    public AjaxResult edit(@RequestBody BaseEntInfo baseEntInfo) {
        baseEntInfo.setBusiState(0);
        baseEntInfo.setUpdateBy(getUsername());
        baseEntInfo.setSecretKey(MD5.create().digestHex(baseEntInfo.getSecretKey()));
        return toAjax(baseEntInfoService.updateById(baseEntInfo));
    }

    /**
     * 删除企业信息
     */
    @PreAuthorize("@ss.hasPermi('ent:info:remove')")
    @Log(title = "企业信息", businessType = BusinessType.DELETE)
    @ApiOperation(value = "删除企业信息")
    @DeleteMapping("/{entIds}")
    public AjaxResult remove(@PathVariable Long[] entIds) {
        return toAjax(baseEntInfoService.removeByIds(Arrays.asList(entIds)));
    }

    /**
     * 修改审核企业信息
     */
//    @PreAuthorize("@ss.hasPermi('ent:info:edit')")
//    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "修改审核企业信息")
    @PostMapping("/updateAndAudit")
    public AjaxResult updateAndAudit(@RequestBody BaseEntInfo baseEntInfo) {
        return baseEntInfoService.updateAndAudit(baseEntInfo);
    }

    @PostMapping("/secondPassword")
    public AjaxResult secondPassword(@RequestBody BaseEntInfo baseEntInfo) {
        String sendSecurity = Md5Utils.hash(baseEntInfo.getSecretKey());
        BaseEntInfo userEnt = baseEntInfoService.getById(SecurityUtils.getLoginUser().getEntId());

        if (sendSecurity.equals(userEnt.getSecretKey())) {
            return AjaxResult.success();
        }
        return AjaxResult.error("二级密码验证失败！");
    }

    /**
     * 修改审核企业信息
     */
    @PreAuthorize("@ss.hasPermi('ent:info:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @ApiOperation(value = "重置企业二级密码")
    @GetMapping("/resetSecretKey")
    public AjaxResult resetSecretKey(Long entId) {
        return baseEntInfoService.resetSecretKey(entId);
    }


    //协会修改限额以下对应用户的统一社会信用代码
    @PostMapping("/editBaseEntInfo")
    public AjaxResult editBaseEntInfo(@RequestBody BaseEntInfo baseEntInfo) {
        return baseEntInfoService.editBaseEntInfo(baseEntInfo);
    }
}
