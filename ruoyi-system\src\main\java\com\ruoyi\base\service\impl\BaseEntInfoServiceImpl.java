package com.ruoyi.base.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.base.mapper.BaseEntInfoMapper;
import com.ruoyi.base.service.IBaseEntInfoService;
import com.ruoyi.busi.enums.AuditBusiTypeEnum;
import com.ruoyi.busi.service.IBusiAuditProcessService;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Md5Utils;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.utils.AttachmentUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.List;

/**
 * 企业信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class BaseEntInfoServiceImpl extends ServiceImpl<BaseEntInfoMapper, BaseEntInfo> implements IBaseEntInfoService {
    @Autowired
    private IBusiAuditProcessService auditProcessService;
    @Autowired
    private ISysUserService iSysUserService;
    /**
     * 查询企业信息列表
     *
     * @param baseEntInfo 企业信息
     * @return 企业信息
     */
    @Override
    public List<BaseEntInfo> selectList(BaseEntInfo baseEntInfo) {
        QueryWrapper<BaseEntInfo> baseEntInfoQueryWrapper = new QueryWrapper<>();
        baseEntInfoQueryWrapper.like(ObjectUtil.isNotEmpty(baseEntInfo.getEntName()), "ent_name", baseEntInfo.getEntName());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntCode()), "ent_code", baseEntInfo.getEntCode());
        baseEntInfoQueryWrapper.like(ObjectUtil.isNotEmpty(baseEntInfo.getEntNature()), "ent_nature", baseEntInfo.getEntNature());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntLinkman()), "ent_linkman", baseEntInfo.getEntLinkman());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntContactPhone()), "ent_contact_phone", baseEntInfo.getEntContactPhone());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntLegalPerson()), "ent_legal_person", baseEntInfo.getEntLegalPerson());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntLegalPersonPhone()), "ent_legal_person_phone", baseEntInfo.getEntLegalPersonPhone());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntStatus()), "ent_status", baseEntInfo.getEntStatus());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntLogo()), "ent_logo", baseEntInfo.getEntLogo());
        baseEntInfoQueryWrapper.eq(ObjectUtil.isNotEmpty(baseEntInfo.getEntType()), "ent_type", baseEntInfo.getEntType());
        baseEntInfoQueryWrapper.orderByDesc("create_time");
        return list(baseEntInfoQueryWrapper);
    }

    @Override
    public List<BaseEntInfo> getByIds(List<Long> entIds) {
        return list(new QueryWrapper<BaseEntInfo>().in("ent_id",entIds));
    }

    @Transactional
    @Override
    public AjaxResult updateAndAudit(BaseEntInfo baseEntInfo)  {
        try {
            BaseEntInfo bi = baseMapper.selectById(baseEntInfo.getEntId());
            if (baseEntInfo.getBusiState() == 0) {
                baseEntInfo.setBusiState(1);
                if (StringUtils.isNotEmpty(bi.getSecretKey())) {
                    String os = Md5Utils.hash(baseEntInfo.getOldSecretKey());
                    if(!bi.getSecretKey().equals(os)){
                        log.error("旧二级密码错误");
                        return AjaxResult.error("旧二级密码错误");
                    }
                }
                baseEntInfo.setSecretKey(Md5Utils.hash(baseEntInfo.getSecretKey()));
                auditProcessService.saveInfo(baseEntInfo.getEntId(), AuditBusiTypeEnum.ENT_INFO, 1, "提交", "提交", 1);
            } else if (baseEntInfo.getBusiState() == 1) {
                if (baseEntInfo.getAuditResult()) {
                    baseEntInfo.setBusiState(10);
                    baseEntInfo.setEntStatus(0);
                    auditProcessService.saveInfo(baseEntInfo.getEntId(), AuditBusiTypeEnum.ENT_INFO, 1, "通过", baseEntInfo.getAuditRemark(), 10);
                } else {
                    baseEntInfo.setBusiState(0);
                    auditProcessService.saveInfo(baseEntInfo.getEntId(), AuditBusiTypeEnum.ENT_INFO, 0, "退回", baseEntInfo.getAuditRemark(), 0);
                }
            }else{
                if (StringUtils.isNotEmpty(bi.getSecretKey())) {
                    String os = Md5Utils.hash(baseEntInfo.getOldSecretKey());
                    if(!bi.getSecretKey().equals(os)){
                        log.error("旧二级密码错误");
                        return AjaxResult.error("旧二级密码错误");
                    }
                }
                baseEntInfo.setSecretKey(Md5Utils.hash(baseEntInfo.getSecretKey()));
            }
            baseMapper.updateById(baseEntInfo);

            return AjaxResult.success();
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getCause().getMessage());
        }
    }

    @Override
    public AjaxResult resetSecretKey(Long entId) {
        BaseEntInfo bi = baseMapper.selectById(entId);
        if(bi!=null){
            bi.setSecretKey(Md5Utils.hash("111111"));
            try {
                if (updateById(bi)) {
                    return AjaxResult.success("重置成功");
                }
                return AjaxResult.error("重置失败");
            } catch (Exception e) {
                e.printStackTrace();
                return AjaxResult.error("重置异常，"+e.getCause().getMessage());
            }
        }
        return AjaxResult.error("企业不存在");
    }

    @Override
    public AjaxResult editBaseEntInfo(BaseEntInfo entInfo) {
    //通过username查存sysUser
        SysUser byUserName = iSysUserService.selectUserByUserName(entInfo.getEntName());
        if (null!=byUserName){
            BaseEntInfo baseEntInfo = this.getById(byUserName.getEntId());
            baseEntInfo.setEntName(entInfo.getEntName());
            baseEntInfo.setEntCode(entInfo.getEntCode());
           // baseEntInfo.setEntType(registerBody.getEntType());
           // baseEntInfo.setEntAddress(registerBody.getEntAddress());
            //baseEntInfo.setEntKeyAgencyAreas(registerBody.getEntKeyAgencyAreas());
            // baseEntInfo.setEntStatus(0);
            baseEntInfo.setBusiState(10);
            //baseEntInfo.setEntContactPhone(registerBody.getEntContactPhone());
            //--测试营业执照复制文件
            //  String targetDir="/home/<USER>/uploadPath";
            String copiedFilePath = copyFileFromUrl(entInfo.getBusinessLicense(), RuoYiConfig.getProfile()+"/upload" );
            String yyzzString = AttachmentUtil.realToUrl(copiedFilePath);
            baseEntInfo.setBusinessLicense(yyzzString);
            //baseEntInfo.setEntLegalPerson(entInfo.getEntLegalPerson());
            //baseEntInfo.setEntLegalPersonPhone(entInfo.getEntLegalPersonPhone());
            //--测试身份证复制文件
            String personCardFilePath = copyFileFromUrl(entInfo.getEntLegalPersonCardFile(), RuoYiConfig.getProfile()+"/upload" );
            String personCardString = AttachmentUtil.realToUrl(personCardFilePath);
            baseEntInfo.setEntLegalPersonCardFile(personCardString);
            //baseEntInfo.setEntLinkman(entInfo.getEntLinkman());
            this.saveOrUpdate(baseEntInfo);
            return AjaxResult.success(baseEntInfo);
        }
        return AjaxResult.success();
    }
    /**
     * 将传入的URL转换为实际地址，并将对应文件拷贝到指定目录
     *
     * @param url        原始URL，如"https://ip.hbszfcgxh.com:10443/zcxh/businessLicense/2025-07-12/2dd3ab01-9add-4e5a-8394-9a21ba0010e1.pdf"
     * @param targetPath 目标拷贝目录路径，如"/path/to/target"
     * @return 拷贝成功返回true，失败返回false
     */
    public static String copyFileFromUrl(String url, String targetPath) {
        try {
            // 提取URL中域名后的路径部分
            int pathStartIndex = url.indexOf("/zcxh/");
            if (pathStartIndex == -1) {
                System.out.println("URL格式不正确，未找到关键路径标识");
                return "";
            }

            String urlPath = url.substring(pathStartIndex);

            // 构建实际地址
            String actualBase = "/data/nginx/html/upload";

            // windows测试  String actualBase = "D:/ruoyi/uploadPath/upload";
            Path actualFilePath = Paths.get(actualBase, urlPath.substring(1)); // 去除路径开头的"/"

            // 检查实际文件是否存在
            if (!Files.exists(actualFilePath) || Files.isDirectory(actualFilePath)) {
                System.out.println("实际文件不存在: " + actualFilePath);
                return "";
            }

            // 确保目标目录存在
            Path targetDirPath = Paths.get(targetPath);
            Files.createDirectories(targetDirPath);

            // 构建目标文件路径
            Path targetFilePath = targetDirPath.resolve(actualFilePath.getFileName());

            // 拷贝文件（保留文件属性）
            Files.copy(actualFilePath, targetFilePath, StandardCopyOption.REPLACE_EXISTING,
                    StandardCopyOption.COPY_ATTRIBUTES);

            System.out.println("文件拷贝成功: " + actualFilePath + " -> " + targetFilePath);
            return targetFilePath.toString(); // 返回拷贝后的文件完整路径


        } catch (IOException e) {
            System.out.println("处理过程出错: " + e.getMessage());
            e.printStackTrace();
            return "";
        }
    }

    public static void main(String[] args) {
        System.out.println(Md5Utils.hash("111111"));
    }
}
