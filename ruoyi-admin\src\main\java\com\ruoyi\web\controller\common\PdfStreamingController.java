package com.ruoyi.web.controller.common;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * PDF流式下载控制器 - 支持大文件分片下载
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/common/pdf")
public class PdfStreamingController {
    
    private static final Logger log = LoggerFactory.getLogger(PdfStreamingController.class);
    
    private static final int DEFAULT_BUFFER_SIZE = 8192;
    private static final long DEFAULT_EXPIRE_TIME = 604800000L; // 7 days
    
    /**
     * 获取PDF文件信息（用于前端预加载）
     * 直接接收完整文件路径，不再提取文件名
     */
    @GetMapping("/info")
    public AjaxResult getPdfInfo(@RequestParam("filePath") String filePath) {
        try {
            log.info("接收到PDF文件路径：{}", filePath);
            
            // 直接使用完整路径，不再提取
            String actualPath = filePath;
            if (!FileUtils.checkAllowDownload(actualPath)) {
                return AjaxResult.error("文件路径非法，不允许访问");
            }
            
            String fullFilePath = RuoYiConfig.getProfile() + actualPath.replace("/profile", "");
//            String fullFilePath = actualPath;

            log.info("完整PDF文件路径：{}", fullFilePath);
            File file = new File(fullFilePath);
            
            if (!file.exists()) {
                log.error("文件不存在：{}", fullFilePath);
                return AjaxResult.error("文件不存在: " + fullFilePath);
            }
            
            AjaxResult result = AjaxResult.success();
            result.put("fileName", file.getName());
            result.put("fileSize", file.length());
            result.put("lastModified", file.lastModified());
            result.put("readable", file.canRead());
            result.put("fullPath", filePath);
            
            return result;
        } catch (Exception e) {
            log.error("获取PDF文件信息失败", e);
            return AjaxResult.error("获取文件信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 支持Range请求的PDF文件流式下载
     * 支持断点续传和分片下载
     */
    @GetMapping("/stream")
    public ResponseEntity<byte[]> streamPdf(
            @RequestParam("filePath") String filePath,
            @RequestHeader(value = "Range", required = false) String range,
            HttpServletResponse response) {
        
        try {
            log.info("接收到PDF流式下载请求，文件路径：{}", filePath);
            
            // 直接使用完整路径，不再提取
            String actualPath = filePath;
            if (!FileUtils.checkAllowDownload(actualPath)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(null);
            }
            
            String fullFilePath = RuoYiConfig.getProfile() + actualPath.replace("/profile", "");
//            String fullFilePath = actualPath;

            log.info("完整PDF文件路径：{}", fullFilePath);
            File file = new File(fullFilePath);
            
            if (!file.exists()) {
                log.error("文件不存在：{}", fullFilePath);
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
            }
            
            long fileLength = file.length();
            
            // 处理Range请求
            if (range != null && range.startsWith("bytes=")) {
                return handleRangeRequest(file, range, fileLength);
            }
            
            // 返回完整文件
            return handleFullFile(file, fileLength);
            
        } catch (Exception e) {
            log.error("PDF流式下载失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }
    
    /**
     * 处理Range请求
     */
    private ResponseEntity<byte[]> handleRangeRequest(File file, String range, long fileLength) throws IOException {
        String[] ranges = range.substring(6).split("-");
        long start = Long.parseLong(ranges[0]);
        long end = ranges.length > 1 ? Long.parseLong(ranges[1]) : fileLength - 1;
        
        if (end >= fileLength) {
            end = fileLength - 1;
        }
        
        long contentLength = end - start + 1;
        
        byte[] data = readFileRange(file, start, end);
        
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/pdf");
        headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(contentLength));
        headers.add(HttpHeaders.CONTENT_RANGE, "bytes " + start + "-" + end + "/" + fileLength);
        headers.add(HttpHeaders.ACCEPT_RANGES, "bytes");
        headers.add(HttpHeaders.CACHE_CONTROL, "max-age=3600");
        
        return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                .headers(headers)
                .body(data);
    }
    
    /**
     * 处理完整文件下载
     */
    private ResponseEntity<byte[]> handleFullFile(File file, long fileLength) throws IOException {
        byte[] data = Files.readAllBytes(file.toPath());
        
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_TYPE, "application/pdf");
        headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(fileLength));
        headers.add(HttpHeaders.ACCEPT_RANGES, "bytes");
        headers.add(HttpHeaders.CACHE_CONTROL, "max-age=3600");
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(data);
    }
    
    /**
     * 读取文件指定范围
     */
    private byte[] readFileRange(File file, long start, long end) throws IOException {
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            long contentLength = end - start + 1;
            byte[] data = new byte[(int) contentLength];
            
            raf.seek(start);
            raf.readFully(data);
            
            return data;
        }
    }
    
    /**
     * 从完整路径中提取实际文件路径
     */
    private String extractActualPath(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return filePath;
        }
        
        // 处理以 /profile/upload/ 开头的路径
        if (filePath.startsWith("/profile/upload/")) {
            return filePath.substring("/profile/upload/".length());
        }
        
        // 处理以 profile/upload/ 开头的路径
        if (filePath.startsWith("profile/upload/")) {
            return filePath.substring("profile/upload/".length());
        }
        
        // 处理包含 upload/ 的路径
        int uploadIndex = filePath.indexOf("upload/");
        if (uploadIndex >= 0) {
            return filePath.substring(uploadIndex + "upload/".length());
        }
        
        // 返回原始路径
        return filePath;
    }
    
    /**
     * 传统下载方式（兼容旧版本）
     */
    @GetMapping("/download")
    public void downloadPdf(@RequestParam("filePath") String filePath,
                           HttpServletResponse response) {
        try {
            log.info("接收到PDF传统下载请求，文件路径：{}", filePath);
            
            // 直接使用完整路径，不再提取
            String actualPath = filePath;
            if (!FileUtils.checkAllowDownload(actualPath)) {
                throw new Exception(StringUtils.format("文件路径({})非法，不允许下载。 ", actualPath));
            }
            
            String fullFilePath = RuoYiConfig.getDownloadPath() + actualPath;
            log.info("完整PDF文件路径：{}", fullFilePath);
            File file = new File(fullFilePath);
            
            if (!file.exists()) {
                log.error("文件不存在：{}", fullFilePath);
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            
            response.setContentType(MediaType.APPLICATION_PDF_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + file.getName() + "\"");
            response.setHeader(HttpHeaders.ACCEPT_RANGES, "bytes");
            
            try (FileInputStream fis = new FileInputStream(file);
                 OutputStream os = response.getOutputStream()) {
                
                byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
            }
            
        } catch (Exception e) {
            log.error("PDF下载失败", e);
        }
    }
}