<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="评分办法细则名称" prop="scoringMethodItemId">
        <el-input
          v-model="queryParams.scoringMethodItemId"
          placeholder="请输入评分办法细则名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="企业id" prop="entId">
        <el-input
          v-model="queryParams.entId"
          placeholder="请输入企业id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评审因素" prop="itemName">
        <el-input
          v-model="queryParams.itemName"
          @keyup.enter.native="handleQuery"
          placeholder="请选择或输入自定义内容"
          filterable
          allow-create
          default-first-option
          clearable
        />
      </el-form-item>
      <el-form-item label="最低分" prop="minScore">
        <el-input
          v-model="queryParams.minScore"
          placeholder="请输入最低分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="最高分" prop="maxScore">
        <el-input
          v-model="queryParams.maxScore"
          placeholder="请输入最高分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['method:uitem:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['method:uitem:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['method:uitem:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['method:uitem:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="uitemList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="评分办法id" align="center" prop="entMethodItemId" />
      <el-table-column label="评分办法细则名称" align="center" prop="scoringMethodItemId" />
      <el-table-column label="企业id" align="center" prop="entId" />
      <el-table-column label="评审因素" align="center" prop="itemName" />
      <el-table-column label="评审内容" align="center" prop="itemRemark" />
      <el-table-column label="最低分" align="center" prop="minScore" />
      <el-table-column label="最高分" align="center" prop="maxScore" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['method:uitem:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['method:uitem:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改用户评分办法因素对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="评分办法细则名称" prop="scoringMethodItemId">
          <el-input v-model="form.scoringMethodItemId" placeholder="请输入评分办法细则名称" />
        </el-form-item>
        <el-form-item label="企业id" prop="entId">
          <el-input v-model="form.entId" placeholder="请输入企业id" />
        </el-form-item>
        <el-form-item label="评审因素" prop="itemName">
          <el-input v-model="form.itemName" placeholder="请输入评审因素" />
        </el-form-item>
        <el-form-item label="评审内容" prop="itemRemark">
          <el-input v-model="form.itemRemark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="最低分" prop="minScore">
          <el-input v-model="form.minScore" placeholder="请输入最低分" />
        </el-form-item>
        <el-form-item label="最高分" prop="maxScore">
          <el-input v-model="form.maxScore" placeholder="请输入最高分" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="删除标记，0正常 1删除" prop="delFlag">
          <el-radio-group v-model="form.delFlag">
            <el-radio
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listUitem, getUitem, delUitem, addUitem, updateUitem } from "@/api/method/uitem";

export default {
  name: "Uitem",
  dicts: ['base_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户评分办法因素表格数据
      uitemList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        scoringMethodItemId: null,
        entId: null,
        itemName: null,
        itemRemark: null,
        minScore: null,
        maxScore: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询用户评分办法因素列表 */
    getList() {
      this.loading = true;
      listUitem(this.queryParams).then(response => {
        this.uitemList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        entMethodItemId: null,
        scoringMethodItemId: null,
        entId: null,
        itemName: null,
        itemRemark: null,
        minScore: null,
        maxScore: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        remark: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.entMethodItemId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户评分办法因素";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const entMethodItemId = row.entMethodItemId || this.ids
      getUitem(entMethodItemId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户评分办法因素";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.entMethodItemId != null) {
            updateUitem(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUitem(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const entMethodItemIds = row.entMethodItemId || this.ids;
      this.$modal.confirm('是否确认删除用户评分办法因素编号为"' + entMethodItemIds + '"的数据项？').then(function() {
        return delUitem(entMethodItemIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('method/uitem/export', {
        ...this.queryParams
      }, `uitem_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
