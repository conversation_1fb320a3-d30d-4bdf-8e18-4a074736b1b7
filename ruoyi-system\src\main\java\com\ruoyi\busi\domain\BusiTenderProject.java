package com.ruoyi.busi.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BusiAuditProcessEntity;
import com.ruoyi.common.core.domain.entity.BaseEntInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 采购项目信息对象 busi_tender_project
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Data
@ApiModel("采购项目信息对象")
@TableName(resultMap = "com.ruoyi.busi.mapper.BusiTenderProjectMapper.BusiTenderProjectResult")
public class BusiTenderProject extends BusiAuditProcessEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long projectId;

    /**
     * 项目代码
     */
    @ApiModelProperty("项目代码")
    @Excel(name = "项目代码")
    private String projectCode;
    /**
     * 项目意向id
     */
    @ApiModelProperty("项目意向id")
    @Excel(name = "项目意向id")
    private Long projectIntentionId;
    /**
     * 紧急项目
     */
    @ApiModelProperty("紧急项目")
    @Excel(name = "紧急项目")
    private Integer isEmergencyProject;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    @Excel(name = "项目名称")
    private String projectName;

    /**
     * 项目开始时间
     */
    @ApiModelProperty("项目开始时间")
    @Excel(name = "项目开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date projectStartTime;

    /**
     * 项目开始时间
     */
    @ApiModelProperty("项目结束时间")
    @Excel(name = "项目结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date projectEndTime;
    /**
     * 项目内容
     */
    @ApiModelProperty("项目内容")
    @Excel(name = "项目内容")
    private String projectContent;
    /**
     * 投标人资格要求
     */
    @ApiModelProperty("投标人资格要求")
    @Excel(name = "投标人资格要求")
    private String bidderQualification;
    /**
     * 面向小微企业 (0不针对 1针对)
     */
    @ApiModelProperty("面向小微企业 (0不针对 1针对)")
    @Excel(name = "面向小微企业 (0不针对 1针对)")
    private Integer toSme;
    /**
     * 项目所属区域
     */
    @ApiModelProperty("项目所属区域")
    @Excel(name = "项目所属区域")
    private String projectArea;
    /**
     * 项目所属行业
     */
    @ApiModelProperty("项目所属行业")
    @Excel(name = "项目所属行业")
    private String projectIndustry;
    /**
     * 采购人id
     */
    @ApiModelProperty("采购人id")
    @Excel(name = "采购人id")
    private Long tendererId;
    /**
     * 采购人代码
     */
    @ApiModelProperty("采购人代码")
    @Excel(name = "采购人代码")
    private String tendererCode;
    /**
     * 采购人名称
     */
    @ApiModelProperty("采购人名称")
    @Excel(name = "采购人名称")
    private String tendererName;
    /**
     * 采购人联系人
     */
    @ApiModelProperty("采购人联系人")
    @Excel(name = "采购人联系人")
    private String tendererContactPerson;
    /**
     * 采购人联系方式
     */
    @ApiModelProperty("采购人联系方式")
    @Excel(name = "采购人联系方式")
    private String tendererPhone;
    /**
     * 采购方式
     */
    @ApiModelProperty("采购方式")
    @Excel(name = "采购方式")
    private String tenderMode;

    @ApiModelProperty("项目类别")
    @Excel(name = "项目类别")
    //0工程，1服务，2货物
    private String projectType;
    /**
     * 是否为代理机构
     */
    @ApiModelProperty("是否为代理机构")
    @Excel(name = "是否为代理机构")
    private Integer agencyFlag;
    /**
     * 代理机构id
     */
    @ApiModelProperty("代理机构id")
    @Excel(name = "代理机构id")
    private Long agencyId;
    /**
     * 代理机构代码
     */
    @ApiModelProperty("代理机构代码")
    @Excel(name = "代理机构代码")
    private String agencyCode;
    /**
     * 代理机构名称
     */
    @ApiModelProperty("代理机构名称")
    @Excel(name = "代理机构名称")
    private String agencyName;
    /**
     * 代理机构联系人
     */
    @ApiModelProperty("代理机构联系人")
    @Excel(name = "代理机构联系人")
    private String agencyContactPerson;
    /**
     * 代理机构联系方式
     */
    @ApiModelProperty("代理机构联系方式")
    @Excel(name = "代理机构联系方式")
    private String agencyPhone;
    /**
     * 预算金额
     */
    @ApiModelProperty("预算金额")
    @Excel(name = "预算金额")
    private BigDecimal budgetAmount;
    /**
     * 控制价
     */
    @ApiModelProperty("控制价")
    @Excel(name = "控制价")
    private BigDecimal controlPrice;
    /**
     * 资金来源
     */
    @ApiModelProperty("资金来源")
    @Excel(name = "资金来源")
    private String tenderFundSource;
    /**
     * 自筹资金
     */
    @ApiModelProperty("自筹资金")
    @Excel(name = "自筹资金")
    private BigDecimal tenderSelfFund;
    /**
     * 财政资金
     */
    @ApiModelProperty("财政资金")
    @Excel(name = "财政资金")
    private BigDecimal tenderFinancialFund;
    /**
     * 项目工期，单位天
     */
    @ApiModelProperty("项目工期，单位天")
    @Excel(name = "项目工期，单位天")
    private Integer projectDuration;
    /**
     * 项目状态
     * 来自字典表
     */
    @ApiModelProperty("项目状态")
    @Excel(name = "项目状态")
    private Integer projectStatus;

    @ApiModelProperty("收费标准")
    @Excel(name = "收费标准")
    private String chargingStandard;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    @TableField(fill = FieldFill.INSERT)
    private Integer delFlag;

    @ApiModelProperty("获取投标人方式（1公开征集、2推荐供应商）")
    private Integer getBidderMode;


    /**
     * 附件列表
     */
    @TableField(exist = false)
    private List<BusiAttachment> attachments;

    //以下为非数据库字段
    /**
     * 采购方式
     */
    @TableField(exist = false)
    private String tenderModeName;
    @TableField(exist = false)
    private String projectTypeName;
    /**
     * 项目行业
     */
    @TableField(exist = false)
    private String projectIndustryName;
    /**
     * 资金来源
     */
    @TableField(exist = false)
    private String tenderFundSourceName;
    /**
     * 项目所属区域
     */
    @TableField(exist = false)
    private String projectAreaCode;
    @TableField(exist = false)
    private String projectAreaName;
    /**
     * 代理信息
     */
    @TableField(exist = false)
    private BaseEntInfo agency;
    /**
     * 采购人信息
     */
    @TableField(exist = false)
    private BaseEntInfo purchaser;
    @TableField(exist = false)
    private Date bidOpeningTime;
    @TableField(exist = false)
    private Integer lastDay;
    @TableField(exist = false)
    private String lxr;
    @TableField(exist = false)
    private String lxdh;
    /**
     * 企业信息
     */
    @TableField(exist = false)
    private BaseEntInfo ent;
    @TableField(exist = false)
    private BusiWinningBidderNotice bidderNotice;
    @TableField(exist = false)
    private BusiTenderNotice notice;
    /**
     * 抽取申请类型
     */
    @TableField(exist = false)
    private Integer extractionType;
    public Long getId(){
        return this.projectId;
    }

    public List<Long> getProjectIndustryShow(){
        List<Long> list = new ArrayList<>();
        if (StringUtils.isNoneBlank(this.projectIndustry)) {
            String[] ss = this.projectIndustry.split(",");
            for (String s : ss) {
                list.add(Long.parseLong(s));
            }
        }
        return list;
    }
    public List<Long> getProjectAreaShow(){
        List<Long> list = new ArrayList<>();
        if (StringUtils.isNoneBlank(this.projectArea)) {
            String[] ss = this.projectArea.split(",");
            for (String s : ss) {
                list.add(Long.parseLong(s));
            }
        }
        return list;
    }

    public String getToSmeName(){
        if (this.getToSme() != null && this.getToSme() == 1) {
            return "本项目支持中小微（监狱、残疾人福利性单位）企业、支持促进残疾人就业等政府采购政策。";
        }
        return "无";
    }

    public String getCreditName(){
        if ("0".equals(this.getProjectType())) {
            return "以“中国政府采购网(www.ccgp.gov.cn)”—政府采购严重违法失信行为记录查询、“中国执行信息公开网（http://zxgk.court.gov.cn）”—失信被执行人查询和“信用中国（www.creditchina.gov.cn”）—信息公示—严重失信主体名单查询、“全国建筑市场监管公共服务平台（https://jzsc.mohurd.gov.cn/）—信用建设—黑名单及失信联合惩戒记录”查询结果为准，对列入失信被执行人、政府采购严重违法失信行为记录名单、黑名单、失信联合惩戒记录名单的投标人，其投标无效，供应商可自行查询截图或提供信用承诺函；";
        }
        return "以“中国政府采购网(www.ccgp.gov.cn)”—政府采购严重违法失信行为记录查询、“中国执行信息公开网（http://zxgk.court.gov.cn）”-失信被执行人查询和“信用中国（www.creditchina.gov.cn）”—信息公示-严重失信主体名单查询为准，对列入失信被执行人、政府采购严重违法失信行为记录名单的投标人，其投标无效，供应商可自行查询截图或提供信用承诺函；";
    }


    /**
     * 获取用于显示的名称（根据代理机构名称和采购人名称判断）
     * @return 显示名称
     */
    public String getDisplayAgencyOrPurchaserName() {
        if (StringUtils.isBlank(agencyName)) {
            return tendererName;
        }
        return agencyName;
    }
    //联系人
    public String getDisplayAgencyOrPurchaserContactPerson() {
        if (StringUtils.isBlank(agencyContactPerson)) {
            return tendererContactPerson;
        }
        return agencyContactPerson;
    }
    //联系电话
    public String getDisplayAgencyOrPurchaserPhone() {
        if (StringUtils.isBlank(agencyPhone)) {
            return tendererPhone;
        }
        return agencyPhone;
    }

}
