package com.ruoyi.busi.service.impl;

import com.ruoyi.base.domain.BaseTreeData;
import com.ruoyi.base.service.IBaseTreeDataService;
import com.ruoyi.busi.domain.BusiTenderNotice;
import com.ruoyi.busi.domain.BusiTenderProject;
import com.ruoyi.busi.domain.BusiWinningBidderNotice;
import com.ruoyi.busi.domain.vo.PortalDataVo;
import com.ruoyi.busi.domain.vo.PortalProcessVo;
import com.ruoyi.busi.domain.vo.TenderProjectVo;
import com.ruoyi.busi.service.IBusiTenderNoticeService;
import com.ruoyi.busi.service.IBusiTenderProjectService;
import com.ruoyi.busi.service.IBusiWinningBidderNoticeService;
import com.ruoyi.busi.service.IPortalService;
import com.ruoyi.system.service.ISysDictDataService;
import freemarker.template.Template;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-06-05
 */
@Service
public class PortalServiceImpl implements IPortalService {
    @Autowired
    private IBusiTenderProjectService tenderProjectService;
    @Autowired
    private IBusiTenderNoticeService tenderNoticeService;
    @Autowired
    private IBusiWinningBidderNoticeService winningBidderNoticeService;
    @Autowired
    private ISysDictDataService sysDictDataService;
    @Autowired
    private IBaseTreeDataService baseTreeDataService;
    @Resource
    private FreeMarkerConfigurer configurer;
    /**
     * 查询列表
     */
    @Override
    public List<PortalDataVo> list(PortalDataVo vo) {
        List<PortalDataVo> voList = new ArrayList<>();
        switch (vo.getDataType()) {
            case 20:
                voList = getNoticeList(vo.initNoticeQuery());
                break;
            case 21:
                voList = getChangeNoticeList(vo.initChangeNoticeQuery());
                break;
            case 60:
                voList = getWinningNoticeList(vo.initWinningNoticeQuery());
                break;
            case -1:
                break;
            default:

        }

        return voList;
    }
    /**
     * 查询列表
     */
    @Override
    public List<PortalDataVo> notOpeninglist(PortalDataVo vo) {
        List<PortalDataVo> voList = new ArrayList<>();
        BusiTenderNotice query = vo.initNoticeQuery();
        query.getParams().put("geBidOpeningTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        voList = getNoticeList(query);
        return voList;
    }

    @Override
    public String info(PortalDataVo vo) {
        String content = "";
        switch (vo.getDataType()) {
            case 20:
                content = getNoticeInfo(vo);
                break;
            case 21:
                content = getChangeNoticeInfo(vo);
                break;
            case 60:
                content = getWinningNoticeInfo(vo);
                break;
            case -1:
                break;
            default:

        }
        return content;
    }

    @Override
    public List<PortalProcessVo> process(PortalDataVo vo) {
        List<PortalProcessVo> processVoList = new ArrayList<>();
        List<BusiTenderNotice> noticeList = tenderNoticeService.selectList(vo.initNoticeQuery());
        if (noticeList != null && noticeList.size() == 1) {
            processVoList.add(PortalProcessVo.exist("20", noticeList.get(0).getNoticeStartTime()));
        }else{
            processVoList.add(PortalProcessVo.notExist("20"));
        }

//        List<BusiTenderNotice> changeNoticeList = tenderNoticeService.selectList(vo.initChangeNoticeQuery());
//        if (changeNoticeList != null && changeNoticeList.size() == 1) {
//            processVoList.add(PortalProcessVo.exist("21", changeNoticeList.get(0).getNoticeStartTime()));
//        }else{
//            processVoList.add(PortalProcessVo.notExist("21"));
//        }

        List<BusiWinningBidderNotice> winningBidderNoticeList = winningBidderNoticeService.selectList(vo.initWinningNoticeQuery());
        if (winningBidderNoticeList != null && winningBidderNoticeList.size() == 1) {
            processVoList.add(PortalProcessVo.exist("60", winningBidderNoticeList.get(0).getNoticeStartTime()));
        }else{
            processVoList.add(PortalProcessVo.notExist("60"));
        }

//        List<Busic> noticeList = tenderNoticeService.selectList(vo.initNoticeQuery());
//        if (noticeList != null && noticeList.size() == 1) {
//            processVoList.add(PortalProcessVo.exist("20", noticeList.get(0).getNoticeStartTime()));
//        }else{
            processVoList.add(PortalProcessVo.notExist("-1"));
//        }
        return processVoList;
    }

    private List<PortalDataVo> getNoticeList(BusiTenderNotice vo){
        List<BusiTenderNotice> noticeList = tenderNoticeService.selectList(vo);

        List<PortalDataVo> voList = new ArrayList<>();
        for (BusiTenderNotice notice : noticeList) {
            BusiTenderProject project = tenderProjectService.getById(notice.getProjectId());
            String dataLabel = sysDictDataService.selectDictLabel("busi_tender_mode", project.getTenderMode());
            project.setTenderModeName(dataLabel);
            String projectIndustry = project.getProjectIndustry();
            if (StringUtils.isNoneBlank(projectIndustry)) {
                String[] industrys = projectIndustry.split(",");
                String lastIndustry = industrys[industrys.length-1];
                BaseTreeData tree = baseTreeDataService.getById(Long.valueOf(lastIndustry));
                project.setProjectIndustryName(tree.getName());
            }
            notice.setProject(project);
            PortalDataVo info = PortalDataVo.initTenderNotice4List(notice);
            voList.add(info);
        }
        return voList;
    }

    private List<PortalDataVo> getChangeNoticeList(BusiTenderNotice vo){
        List<BusiTenderNotice> noticeList = tenderNoticeService.selectList(vo);

        List<PortalDataVo> voList = new ArrayList<>();
        for (BusiTenderNotice notice : noticeList) {
            BusiTenderProject project = tenderProjectService.getById(notice.getProjectId());
//            TenderProjectVo projectVo = (TenderProjectVo)project;
            String dataLabel = sysDictDataService.selectDictLabel("busi_tender_mode", project.getTenderMode());
            project.setTenderModeName(dataLabel);
            notice.setProject(project);
            PortalDataVo info = PortalDataVo.initTenderNotice4List(notice);
            voList.add(info);
        }
        return voList;
    }

    private List<PortalDataVo> getWinningNoticeList(BusiWinningBidderNotice vo){
        vo.getParams().put("isScope", "false");
        List<BusiWinningBidderNotice> noticeList = winningBidderNoticeService.selectList(vo);

        List<PortalDataVo> voList = new ArrayList<>();
        for (BusiWinningBidderNotice notice : noticeList) {
            BusiTenderProject project = tenderProjectService.getById(notice.getProjectId());
            String dataLabel = sysDictDataService.selectDictLabel("busi_tender_mode", project.getTenderMode());
            project.setTenderModeName(dataLabel);
            notice.setProject(project);
            PortalDataVo info = PortalDataVo.initWinningNotice4List(notice);
            voList.add(info);
        }
        return voList;
    }

    private String getNoticeInfo(PortalDataVo vo){
//        List<BusiTenderNotice> noticeList = tenderNoticeService.selectList(vo.initNoticeQuery());
//        String res = "";
//        if (noticeList!=null && noticeList.size()==1) {
//            BusiTenderNotice notice = noticeList.get(0);
//            BusiTenderProject project = tenderProjectService.getById(notice.getProjectId());
////            TenderProjectVo projectVo = (TenderProjectVo)project;
//            String dataLabel = sysDictDataService.selectDictLabel("busi_tender_mode", project.getTenderMode());
//            project.setTenderModeName(dataLabel);
//            notice.setProject(project);
//            PortalDataVo info = PortalDataVo.initTenderNotice4Info(notice);
//            try {
//                Template template = configurer.getConfiguration().getTemplate("portal_templates/采购公告.ftl");
//                res = FreeMarkerTemplateUtils.processTemplateIntoString(template, info);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
        BusiTenderNotice notice = tenderNoticeService.getOneIgnoreDeleted(vo.initNoticeQuery());
        String res = "";
        if (notice!=null) {
           res = notice.getMainContent();
        }
        return res;
    }

    private String getChangeNoticeInfo(PortalDataVo vo){
//        List<BusiTenderNotice> noticeList = tenderNoticeService.selectList(vo.initChangeNoticeQuery());
//        String res = "";
//
//        if (noticeList!=null && noticeList.size()==1) {
//            BusiTenderNotice notice = noticeList.get(0);
//            BusiTenderProject project = tenderProjectService.getById(notice.getProjectId());
////            TenderProjectVo projectVo = (TenderProjectVo)project;
//            String dataLabel = sysDictDataService.selectDictLabel("busi_tender_mode", project.getTenderMode());
//            project.setTenderModeName(dataLabel);
//            notice.setProject(project);
//            PortalDataVo info = PortalDataVo.initTenderNotice4Info(notice);
//            try {
//                Template template = configurer.getConfiguration().getTemplate("portal_templates/变更公告.ftl");
//                res = FreeMarkerTemplateUtils.processTemplateIntoString(template, info);
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
        BusiTenderNotice notice = tenderNoticeService.getOneIgnoreDeleted(vo.initNoticeQuery());
        String res = "";
        if (notice!=null) {
            res = notice.getMainContent();
        }
        return res;
    }

    private String getWinningNoticeInfo(PortalDataVo vo){
        List<BusiWinningBidderNotice> noticeList = winningBidderNoticeService.selectList(vo.initWinningNoticeQuery());
        String res = "";

        PortalDataVo info = new PortalDataVo();
        if (noticeList!=null && noticeList.size()==1) {
            BusiWinningBidderNotice notice = noticeList.get(0);
            res = notice.getNoticeContent();
//            BusiTenderProject project = tenderProjectService.getById(notice.getProjectId());
//            String dataLabel = sysDictDataService.selectDictLabel("busi_bidder_notice_attachment", project.getTenderMode());
//            project.setTenderModeName(dataLabel);
//            notice.setProject(project);
//            info = PortalDataVo.initWinningNotice4Info(notice);
        }
        return res;
    }
}
