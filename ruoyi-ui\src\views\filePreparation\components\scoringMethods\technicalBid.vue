<template>
  <div class="container">
    <div class="transfer">
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <span>合计： <strong>{{tempItem.score}}</strong>分</span>
        <el-button
          class="item-button"
          style="background-color: #fff; height: 40px; color: #333"
          @click="open()"
          >新增</el-button
        >
      </div>
      <el-table :data="itemInfos" border style="width: 100%">
        <el-table-column label="序号" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="itemName" label="评审因素" width="180" align="center">
        </el-table-column>
        <el-table-column prop="itemRemark" label="评审内容" align="center">
        </el-table-column>
        <el-table-column label="分值" align="center">
          <template slot-scope="scope">
            <div style="margin-bottom: 5px">
              <span>评分挡位:</span>
              {{ scope.row.scoreLevel }}
              <!-- <el-input
                v-model="scope.row.maxScore"
                style="width: 80px"
              ></el-input> -->
            </div>
            <div>
              <span>分值:</span>
              {{ scope.row.score }}
              <!-- <el-input
                v-model="scope.row.minScore"
                style="width: 80px"
              ></el-input> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template slot-scope="scope">
	          <el-button @click="handleClick(scope.row)" type="text" size="small" >删除</el-button>
	          <el-button @click="open(scope.row)" type="text" size="small">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog :title="title" :visible.sync="dialogVisible" width="40%">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="评审因素" prop="itemName">
          <el-input
            maxlength="200"
            show-word-limit
            type="textarea"
            :rows="4"
            v-model="form.itemName"
            placeholder="请选择或输入自定义内容"
            filterable
            allow-create
            default-first-option
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="评审内容" prop="itemRemark">
          <el-input v-model="form.itemRemark" show-word-limit type="textarea" :rows="8">{{ form.itemRemark }}</el-input>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分数挡位" prop="scoreLevel">
              <el-input v-model="form.scoreLevel"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分值" prop="score">
              <el-input v-model="form.score"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleUpdate()">保 存</el-button>
        <el-button @click="handleCancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { saveInfo as saveUinfo, updateUinfo } from "@/api/method/uinfo";
import { delUitem, listUitem, updateUitem, addUitem } from "@/api/method/uitem";
export default {
  data() {
    return {
      itemInfos: [],
      dialogVisible: false,
      form: {
        entMethodItemId: "",
        itemName: "",
        itemRemark: "",
        scoreLevel: "",
        score: "",
      },
      rules: {
        itemName: [
          { required: true, message: "请输入评审因素", trigger: "blur" },
        ],
        itemRemark: [
          { required: true, message: "请输入评审内容", trigger: "blur" },
        ],
        scoreLevel: [
          {
            required: true,
            message: "请输入分数挡位使用逗号分割",
            trigger: "blur",
          },
        ],
        score: [{ required: true, message: "请输入分值", trigger: "blur" }],
      },
      projectInfo: {},
      uinfo: {},
      tempItem: {},
      activeParams: {},
      title: "新增评审信息",
    };
  },
  methods: {
    // 重新加载数据的方法
    refreshTable() {
      if (!this.uInfo || !this.uInfo.entMethodId) {
        console.error("refreshTable: uInfo 或 entMethodId 不存在", this.uInfo);
        return;
      }
      if (!this.tempItem || !this.tempItem.scoringMethodItemId) {
        console.error("refreshTable: tempItem 或 scoringMethodItemId 不存在", this.tempItem);
        return;
      }

      let query = {
        entMethodId: this.uInfo.entMethodId,
        scoringMethodItemId: this.tempItem.scoringMethodItemId,
        entId: this.uInfo.entId,
      };
      console.log("refreshTable query:", query);

      listUitem(query).then((result) => {
        if (result && result.rows) {
          this.itemInfos = result.rows;
          console.log("refreshTable success", result.rows);
        } else {
          console.warn("refreshTable: 返回数据格式异常", result);
        }
      }).catch((error) => {
        console.error("refreshTable error", error);
      });
    },
    //初始化信息
    init(projectInfo, itemInfos, uinfo, tempItem, activeParams) {
      console.log("technicalBid init start", itemInfos);
      this.projectInfo = projectInfo;
      this.activeParams = activeParams;
      this.itemInfos = itemInfos;
      this.uInfo = uinfo || {};
      this.tempItem = tempItem;
      console.log("technicalBid init end", this.itemInfos, this.tempItem, this.uInfo);
    },
    //删除
    handleClick(row) {
      delUitem(row.entMethodItemId)
        .then((result) => {
          if (result.code === 200) {
            this.$message.success("删除成功");
            this.itemInfos.splice(this.itemInfos.indexOf(row), 1);
            this.$emit("deleteSuccess", this.activeParams);
          }
        })
        .catch((err) => { });
    },
    // 新建开标项
    open(row) {
      if (row) {
        this.title = "修改评审信息";
        this.form.entMethodItemId = row.entMethodItemId;
        this.form.itemName = row.itemName;
        this.form.itemRemark = row.itemRemark;
        this.form.scoreLevel = row.scoreLevel;
        this.form.score = row.score;
      } else {
        this.title = "新增评审信息";
        this.form.entMethodItemId = "";
        this.form.itemName = "";
        this.form.itemRemark = "";
        this.form.scoreLevel = "";
        this.form.score = "";
      }
      this.dialogVisible = true;
    },
    // 确认
    handleConfirm() {
      let a = {
        itemName: this.form.itemName,
        itemRemark: this.form.itemRemark,
        scoreLevel: this.form.scoreLevel,
        score: this.form.score,
      };
      if (this.$refs.form) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.itemInfos.push(a);
            this.saveUinfo();
            this.dialogVisible = false;
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      } else {
        console.error("Form ref is undefined");
      }
    },
    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },
    saveUinfo() {
      const postData = {
        entMethodId: this.uInfo.entMethodId,
        scoringMethodId: this.tempItem.scoringMethodId,
        projectId: this.projectInfo.projectId,
        scoringMethodUitems: this.itemInfos.map((item) => {
          return {
            entMethodId: this.uInfo.entMethodId,
            entMethodItemId: item.entMethodItemId || "", // 确保新增项也能正确处理
            scoringMethodId: this.tempItem.scoringMethodId,
            scoringMethodItemId: this.tempItem.scoringMethodItemId,
            itemRemark: item.itemRemark,
            itemName: item.itemName,
            scoreLevel: item.scoreLevel,
            score: item.score,
          };
        }),
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
    handleUpdate() {
      if(this.form.scoreLevel!=null && this.form.scoreLevel!=''){
        const parts = this.form.scoreLevel.split(','); // 以逗号为分隔符
        if(this.form.score != parts[parts.length-1]){
          this.$message.warning("挡位最大值必须等于分数最大值");
          return
        }
      }
      if (this.$refs.form) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (this.form.entMethodItemId == "") {
              // 新增
              let putData = {
                entMethodItemId: this.form.entMethodItemId,
                itemName: this.form.itemName,
                itemRemark: this.form.itemRemark,
                scoreLevel: this.form.scoreLevel,
                score: this.form.score,
                entMethodId: this.uInfo.entMethodId,
                scoringMethodId: this.tempItem.scoringMethodId,
                scoringMethodItemId: this.tempItem.scoringMethodItemId,
                entId: this.uInfo.entId,
              };
              addUitem(putData).then((response) => {
                if (response.code === 200) {
                  this.$message.success("新增成功");

                  // 立即在本地添加新增的数据
                  if (response.data && response.data.entMethodItemId) {
                    const newItem = {
                      entMethodItemId: response.data.entMethodItemId,
                      itemName: this.form.itemName,
                      itemRemark: this.form.itemRemark,
                      scoreLevel: this.form.scoreLevel,
                      score: this.form.score,
                    };
                    this.itemInfos.push(newItem);
                    console.log("新增成功，本地列表已更新", newItem);
                  }

                  // 同时刷新数据作为备选方案
                  this.refreshTable();
                  this.dialogVisible = false;
                  this.$emit("saveSuccess", response.data);
                }
              }).catch((error) => {
                console.error("新增失败", error);
                this.$message.error("新增失败");
              });
            } else {
              // 修改
              let putData = {
                entMethodItemId: this.form.entMethodItemId,
                itemName: this.form.itemName,
                itemRemark: this.form.itemRemark,
                scoreLevel: this.form.scoreLevel,
                score: this.form.score,
              };
              updateUitem(putData).then((response) => {
                if (response.code === 200) {
                  this.$message.success("修改成功");

                  // 立即在本地更新修改的数据
                  const index = this.itemInfos.findIndex(item => item.entMethodItemId === this.form.entMethodItemId);
                  if (index !== -1) {
                    this.itemInfos[index] = {
                      ...this.itemInfos[index],
                      itemName: this.form.itemName,
                      itemRemark: this.form.itemRemark,
                      scoreLevel: this.form.scoreLevel,
                      score: this.form.score,
                    };
                    console.log("修改成功，本地列表已更新", this.itemInfos[index]);
                  }

                  // 同时刷新数据作为备选方案
                  this.refreshTable();
                  this.dialogVisible = false;
                  this.$emit("updateSuccess", response.data);
                }
              }).catch((error) => {
                console.error("修改失败", error);
                this.$message.error("修改失败");
              });
            }
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      } else {
        console.error("Form ref is undefined");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.delete-icon {
  width: 25px;
  height: 25px;
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-itemremark: center; /* 垂直居中 */
}
.transfer {
  width: 70%;
  margin-bottom: 20px; /* 添加底部间距，使 transfer 和按钮之间有一定的距离 */
}
.item {
  display: flex;
  justify-itemremark: center;
  align-items: center;
  font-size: 18px;
}
.item-button {
  border: #333 1px solid;
  color: #fff;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #fff;
  }
}
</style>
