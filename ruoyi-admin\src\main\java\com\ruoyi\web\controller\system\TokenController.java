package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.ServletUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.framework.web.service.SysPermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Token刷新接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/token")
public class TokenController {
    
    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private SysPermissionService permissionService;

    /**
     * 刷新token
     */
    @PostMapping("/refresh")
    public AjaxResult refresh() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        if (loginUser != null) {
            // 刷新token
            tokenService.refreshToken(loginUser);
            
            // 重新生成权限信息
            loginUser.setPermissions(permissionService.getMenuPermission(loginUser.getUser()));
            
            // 生成新的token
            String newToken = tokenService.createToken(loginUser);
            
            Map<String, Object> result = new HashMap<>();
            result.put("token", newToken);
            result.put("expireTime", loginUser.getExpireTime());
            
            return AjaxResult.success(result);
        }
        
        return AjaxResult.error("刷新token失败");
    }
}