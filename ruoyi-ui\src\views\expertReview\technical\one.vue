<template>
  <div class="technical-review-container">
    <div class="main-content">
      <div class="header-section">
        <div class="title-section">
          <div class="main-title">技术标评审</div>
          <div class="help-section">
            <div class="help-text">该页面操作说明</div>
            <el-image class="help-image" :src="srcList[0]" :preview-src-list="srcList">
            </el-image>
          </div>
        </div>

        <!-- 文件列表 -->
        <div class="file-list-container">
          <div class="file-list-title">响应文件附件下载</div>
	        <div class="fileListInner">
	          <el-card
	            v-for="(item, index) in attachmentsList"
	            :key="index"
	            class="file-item"
	            shadow="hover"
	            @click.native="downloadFile(item)"
	          >
	            <div class="file-item-content">
	              <i class="el-icon-document file-icon"></i>
	              <span class="file-name">{{ item.fileName }}</span>
	              <i class="el-icon-download download-icon"></i>
	            </div>
	          </el-card>
	        </div>
        </div>

        <div class="action-buttons">
          <el-button class="item-button" @click="bidInquiry">询标</el-button>
          <el-button class="item-button" v-if="expertInfo.expertLeader==1" @click="secondOffer">发起二次报价</el-button>
          <div class="button-group">
	          <el-button
	           :class="['item-button', activeButton === 'procurement' ? 'technical-blue-btn-active' : 'technical-blue-btn']"
	           @click="viewPurchasing"
	            :disabled="isPdfRendering">采购文件</el-button>
	           
	            <el-button
	              :class="['item-button', activeButton === 'response' ? 'technical-blue-btn-active' : 'technical-blue-btn']"
	              @click="showResponseFile()"
	              :disabled="isPdfRendering">响应文件</el-button>
	            
	            <el-button
	              :class="['item-button', activeButton === 'contrast' ? 'technical-blue-btn-active' : 'technical-blue-btn']"
	              @click="fileContrast"
	              :disabled="isPdfRendering">对比</el-button>
          </div>
        </div>
      </div>
      <div class="content-section">
        

        <!-- PDF预览区域 - 保持原始尺寸 -->
        <div class="pdf-preview-area">
          <div
            v-show="procurementShow"
            class="pdf-panel procurement-panel"
            :class="{ 'border-left': double }"
          >
<!--            <pdfView-->
<!--              ref="procurement"-->
<!--              :pdfurl="procurementPdf"-->
<!--              :uni_key="'procurement'"-->
<!--            ></pdfView>-->
           
           <PdfViewImproved v-if="procurementShow" ref="procurement"  :pdfurl="procurementPdf"  :page-height="800" :buffer-size="2" @render-status-change="(status) => handlePdfRenderStatusChange(status, 'procurement')"/>
          </div>
         
         <div
          v-show="responseShow"
          class="pdf-panel response-panel"
          :class="{ 'border-right': double }"
         >
          <!--            <pdfView-->
          <!--              ref="response"-->
          <!--              :pdfurl="responsePdf"-->
          <!--              :uni_key="'response'"-->
          <!--            ></pdfView>-->
          
          <PdfViewImproved v-if="responseShow" ref="response"  :pdfurl="responsePdf"  :page-height="800" :buffer-size="2" @render-status-change="(status) => handlePdfRenderStatusChange(status, 'response')"/>
         </div>
        </div>
      </div>
    </div>
    <div class="divider">
    </div>
    <div class="sidebar">
      <div class="sidebar-header">
        <el-select
          class="supplier-select"
          v-model="supplier"
          placeholder="请选择供应商"
          @change="handleChange"
          :disabled="isPdfRendering"
        >
          <el-option
            v-for="item in options"
            :key="item.bidderName"
            :label="item.bidderName"
            :value="item.bidderName"
          >
          </el-option>
        </el-select>
      </div>
	    
      <div class="sidebar-content" >
        <!-- 响应文件评分项显示 -->
	      <template v-if="responseShow || double">
		      <!-- PDF渲染状态提示 -->
		      <div v-if="!responsePdfRendered" class="render-status-tip">
			      <i class="el-icon-loading"></i>
			      <span>响应文件正在渲染中，请稍候...</span>
		      </div>
		      <div v-else class="render-status-tip success">
			      <i class="el-icon-success"></i>
			      <span>响应文件渲染完成，可以点击跳转</span>
		      </div>
		      
		      <div
			      v-for="(item, index) in scoringSystem.uitems"
			      :key="'response-' + index"
			      class="factor-item"
			      @mouseenter="showFactorTooltip(item)"
			      @mouseleave="hideFactorTooltip"
		      >
			      <!-- 悬浮框 -->
			      <div v-if="hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId"
			           class="factor-tooltip"
			           @mouseenter="clearTooltipTimer"
			           @mouseleave="hideFactorTooltip">
				      <div class="tooltip-header">
					      <div class="tooltip-title">评审内容</div>
					      <i class="el-icon-close tooltip-close" @click="hideFactorTooltip"></i>
				      </div>
				      <div class="tooltip-content" v-html="item.itemRemark"></div>
			      </div>
			      <div>
				      <div class="factors">
					      <div class="factor-header">
						      <div class="factor-name factor-title"
						           :class="{ 'disabled': !canJumpToPage() }"
						           @click="showInfo(item)">
							      {{ item.itemName }}
							      <i v-if="!canJumpToPage()" class="el-icon-loading" style="margin-left: 5px; font-size: 12px;"></i>
							      
							      <span class="max-score">
							        最高{{ getMaxScore(item) }}分
							      </span>
						      </div>
					      </div>
					      <div class="factor-input">
						      <div v-if="!(item.scoreLevel.length == 0 || item.scoreLevel == null || item.scoreLevel == undefined)">
							      <el-radio
								      v-for="(score,index) in item.scoreLevel.split(',')"
								      :key="index"
								      v-model="defaultRatingArray[item.entMethodItemId].state"
								      :label="score"
							      ><span class="score-value">{{ score }}</span></el-radio>
						      </div>
						      <div v-else>
							      <el-input
								      placeholder="请输入分数"
								      v-model="defaultRatingArray[item.entMethodItemId].state"
								      @input="handleScoreInput(item.entMethodItemId, $event)"
								      @keypress="onlyNumber"
								      type="number"
								      step="1"
								      min="0"
							      ></el-input>
						      </div>
					      </div>
				      
				      </div>
			      </div>
		      </div>
	      </template>
	      
	      <template v-else-if="procurementShow" >
		      
		      <!-- PDF渲染状态提示 -->
          <div v-if="!procurementPdfRendered" class="render-status-tip">
            <i class="el-icon-loading"></i>
            <span>采购文件正在渲染中，请稍候...</span>
          </div>
          <div v-else class="render-status-tip success">
            <i class="el-icon-success"></i>
            <span>采购文件渲染完成，可以点击跳转</span>
          </div>
		      
		      <!-- 采购文件评分项显示 -->
		      <div
			      v-for="(item, index) in pageProcurement"
			      :key="'procurement-' + index"
			      class="factor-item"
			      @mouseenter="showFactorTooltip(item)"
			      @mouseleave="hideFactorTooltip"
		      >
			      <!-- 悬浮框 -->
			      <div v-if="hoveredFactorNode && hoveredFactorNode.entMethodItemId === item.entMethodItemId"
			           class="factor-tooltip"
			           @mouseenter="clearTooltipTimer"
			           @mouseleave="hideFactorTooltip">
				      <div class="tooltip-header">
					      <div class="tooltip-title">评审内容</div>
					      <i class="el-icon-close tooltip-close" @click="hideFactorTooltip"></i>
				      </div>
				      <div class="tooltip-content" v-html="item.itemRemark"></div>
			      </div>
			      <div>
				      <div class="factors">
					      <div class="factor-header">
						      <div class="factor-name factor-title"
						           :class="{ 'disabled': !canJumpToPage() }"
						           @click="jumpToProcurementPage(item)">
							      {{ item.itemName }}
							      
							      <i v-if="!canJumpToPage()" class="el-icon-loading" style="margin-left: 5px; font-size: 12px;"></i>
						      </div>
					      </div>
				      </div>
			      </div>
		      </div>
	      </template>
	      
        <div class="submit-section">
          <!-- <div><el-button
              class="item-button-little"
              style="background-color:#F5F5F5;color:#176ADB"
              @click="save"
            >保存</el-button></div> -->
          <div><el-button
              class="item-button-little primary-btn"
              @click="submit"
            >提交</el-button></div>
        </div>

        <div class="review-content">
          <div class="review-title">评审内容：</div>
          <div class="review-text" v-html="selectNode.itemRemark"></div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import {
  supplierInfo,
  approvalProcess,
  scoringFactors,
  checkReviewSummary,
  filesById,
} from "@/api/expert/review";
import { getDetailByPsxx } from "@/api/evaluation/detail/";
import { editEvalExpertScoreInfo } from "@/api/evaluation/expertStatus";

export default {
  data() {
    return {
      options: [],
      scoringSystem: [],
      selectNode: {},
      supplier: "",
      selectSupplier: {},
      expertInfo: {},
      defaultRatingArray: {},
      file: {},
      responseShow: false,
      procurementShow: false,
      double: false,
      factorList: [],
      entDocResponsePage: {},
      factorsPage: {},
      bidderFactor: {},

      responsePdf: null,
      procurementPdf: null,
      currentMaxScore: null,

      // 按钮状态管理
      activeButton: 'response', // 当前激活的按钮：'response'、'procurement'、'contrast'

      // 采购文件相关数据
      entDocProcurementPage: {}, // 采购文件页码信息
      pageProcurement: [], // 采购文件的评分项
	    attachmentsList:[], // 文件列表
	    
	    // PDF渲染状态管理
	   responsePdfRendered: false, // 响应文件PDF是否渲染完成
	   procurementPdfRendered: false, // 采购文件PDF是否渲染完成
	    isPdfRendering: false, // PDF是否正在渲染中

	    srcList: ["/evalution/help.jpg"],
	   
	   // 悬停状态管理
	   hoveredFactorNode: null, // 悬停时的评分项
	   tooltipTimer: null, // 悬浮框显示定时器
    };
  },
  methods: {
    /**
     * 限制输入框只能输入数字和小数点
     * @param {Event} event - 键盘事件
     */
    onlyNumber(event) {
      // 获取按键的字符码
      const charCode = event.which || event.keyCode;

      // 允许的字符：数字(48-57)、小数点(46)、退格(8)、删除(46)、Tab(9)、Enter(13)、方向键(37-40)
      if (
        (charCode >= 48 && charCode <= 57) || // 数字 0-9
        charCode === 46 || // 小数点
        charCode === 8 ||  // 退格键
        charCode === 9 ||  // Tab键
        charCode === 13 || // Enter键
        (charCode >= 37 && charCode <= 40) // 方向键
      ) {
        return true;
      }

      // 阻止其他字符输入
      event.preventDefault();
      return false;
    },

    /**
     * 处理分数输入，确保只能输入有效的数字
     * @param {string} itemId - 评估项ID
     * @param {string} value - 输入值
     */
    handleScoreInput(itemId, value) {
      // 移除非数字字符（保留小数点）
      let cleanValue = value.replace(/[^\d.]/g, '');

      // 确保只有一个小数点
      const parts = cleanValue.split('.');
      if (parts.length > 2) {
        cleanValue = parts[0] + '.' + parts.slice(1).join('');
      }

      // 限制小数点后最多2位
      if (parts.length === 2 && parts[1].length > 2) {
        cleanValue = parts[0] + '.' + parts[1].substring(0, 2);
      }

      // 更新值
      this.defaultRatingArray[itemId].state = cleanValue;

      // 调用原有的验证方法
      this.validateScore(itemId, cleanValue);
    },

    init() {
      const expertInfo = JSON.parse(localStorage.getItem("expertInfo"));
      this.entDocResponsePage = JSON.parse(
        localStorage.getItem("entDocResponsePage")
      );
      // 初始化采购文件页码信息
      this.entDocProcurementPage = JSON.parse(localStorage.getItem("entDocProcurementPage"));
      supplierInfo({ projectId: this.$route.query.projectId }).then(
        (response) => {
          if (response.code == 200) {
            this.options = response.rows.filter(item => item.isAbandonedBid == 0);
          } else {
            this.$message.warning(response.msg);
          }
        }
      );
      approvalProcess(this.$route.query.projectId, expertInfo.resultId).then(
        (response) => {
          if (response.code == 200) {
						// 文件列表
	          this.attachmentsList = response.data.busiTenderNotice.attachments.filter(item => item.fileType == "0");
						
            this.scoringSystem =
              response.data.scoringMethodUinfo.scoringMethodItems.find(
                (item) => {
                  return (
                    item.scoringMethodItemId ==
                    this.$route.query.scoringMethodItemId
                  );
                }
              );
            localStorage.setItem(
              "evalProjectEvaluationProcess",
              JSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)
            );
            // TODO 首先生成长度为this.scoringSystem.length的数组，结构为{state：false，reason：“”}
            this.defaultRatingArray = this.scoringSystem.uitems.reduce(
              (acc, _, index) => {
                acc[this.scoringSystem.uitems[index].entMethodItemId] = {
                  state: null,
                  reason: "",
                };
                console.log("scoringSystem", this.scoringSystem);
                return acc;
              },
              {}
            );
          } else {
            this.$messgae.warning(response.msg);
          }
        }
      );
      filesById(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.file = response.data;
          // 注释掉自动设置采购文件，改为点击时才设置
          // if (this.file.tenderNoticeFilePath != undefined) {
          //   this.procurementPdf = this.file.tenderNoticeFilePath;
          // }
          // 不自动设置响应文件，只在需要时才设置
          // if (this.file.file != undefined) {
          //   this.responsePdf = this.file.file[0];
          // }
        } else {
          this.$message.warning(response.msg);
        }
      });
      // 初始化专家信息
      this.initExpertInfo();
    },

    /**
     * 初始化专家信息
     */
    initExpertInfo() {
      try {
        const itemString = localStorage.getItem("expertInfo");
        if (itemString) {
          this.expertInfo = JSON.parse(itemString);
          console.log("专家信息已初始化", this.expertInfo);
        } else {
          console.warn("localStorage中未找到expertInfo");
        }
      } catch (error) {
        console.error("初始化专家信息失败:", error);
      }
    },

    handleChange(value) {
      if(Object.keys(this.selectSupplier).length != 0){
        this.tmpSave();
      }
      this.selectSupplier = this.options.find((item) => {
        return item.bidderName == value;
      });

      // 根据bidderid获取供应商因素及其对应页码
      this.bidderFactor = this.factorsPage[this.selectSupplier.bidderId];

      const data = {
        expertResultId: this.expertInfo.resultId,
        projectId: this.$route.query.projectId,
        scoringMethodItemId: this.$route.query.scoringMethodItemId,
      };
      getDetailByPsxx(data).then((response) => {
        if (response.code == 200) {
          this.factorList = response.data;
          const factor = this.factorList.find((item) => {
            return item.bidderName == value;
          }).evalExpertEvaluationDetails;
          if (factor != null) {
            factor.map((item) => {
              this.defaultRatingArray[item.scoringMethodUitemId].reason =
                item.evaluationRemark;
              this.defaultRatingArray[item.scoringMethodUitemId].state =
                item.evaluationResult;
            });
          } else {
            Object.keys(this.defaultRatingArray).forEach((key) => {
              this.defaultRatingArray[key].state = null;
              this.defaultRatingArray[key].reason = "";
            });
          }
        } else {
          this.$message.warning(response.msg);
        }
      });
      this.showResponseFile();
      // 重置最大分数值
      // this.currentMaxScore = null;
    },
	  
	  
    validateScore(itemId, event) {
      const inputValue = parseFloat(event);
      console.log("inputValue", inputValue);
      
      // 获取当前评分项的最大分值
      const currentItem = this.scoringSystem.uitems.find(item => item.entMethodItemId === itemId);
      let maxScore = null;
      
      if (currentItem) {
        // 如果有分数挡位，使用挡位中的最大值
        if (currentItem.scoreLevel && currentItem.scoreLevel.length > 0) {
          const scoreLevels = currentItem.scoreLevel.split(',').map(item => parseFloat(item.trim())).filter(item => !isNaN(item));
          if (scoreLevels.length > 0) {
            maxScore = Math.max(...scoreLevels);
          }
        } else {
          // 否则使用配置的最大分值
          maxScore = parseFloat(currentItem.score);
        }
      }
      
      console.log("maxScore", maxScore);

      if (!isNaN(inputValue) && maxScore !== null) {
        if (inputValue > maxScore) {
          this.$message.warning(`输入分数不能超过${maxScore}分，请重新输入`);
          // 将输入值限制为最大分数值
          this.defaultRatingArray[itemId].state = "";
        } else if (inputValue < 0) {
          this.$message.warning("输入分数不能小于0分");
          this.defaultRatingArray[itemId].state = "";
        }
      }
    },
    showResponseFile() {
      if (Object.keys(this.selectSupplier).length === 0) {
        this.$message.warning("请选择供应商");
      } else {
        this.activeButton = 'response'; // 设置当前激活按钮
        this.double = false;
        this.procurementShow = false;
        this.responseShow = true;
        this.responsePdf = this.file.file[this.selectSupplier.bidderId];
        this.isPdfRendering = true;
      }
    },
    fileContrast() {
      if (Object.keys(this.selectSupplier).length === 0) {
        this.$message.warning("请选择供应商");
      } else {
        this.activeButton = 'contrast'; // 设置当前激活按钮
        this.double = true;
        this.procurementShow = true;
        this.responseShow = true;
        this.responsePdf = this.file.file[this.selectSupplier.bidderId];
        // 设置采购文件PDF地址
        if (this.file.tenderNoticeFilePath) {
          this.procurementPdf = this.file.tenderNoticeFilePath;
        }
        this.isPdfRendering = true;
      }
    },
    showInfo(item) {
	    // 检查PDF是否渲染完成
	    if (!this.canJumpToPage()) {
		    this.$message.warning("PDF页面正在渲染中，请稍候再试");
		    return;
	    }
			
      this.selectNode = item;

      // 如果只显示采购文件，使用采购文件页码信息
      if (this.procurementShow && !this.responseShow) {
	      if (!this.procurementPdfRendered) {
		      this.$message.warning("采购文件正在渲染中，请稍候再试");
		      return;
	      }
				
        if (item.jumpToPage) {
          this.$refs.procurement.skipPage(item.jumpToPage);
        } else if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {
          this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);
        }
        return;
      }

      // 如果显示响应文件或对比模式，需要选择供应商
      if (Object.keys(this.bidderFactor).length != 0) {
        // 跳转到响应文件对应页码
        if (this.responseShow && this.$refs.response) {
	        if (!this.responsePdfRendered) {
		        this.$message.warning("响应文件正在渲染中，请稍候再试");
		        return;
	        }
          this.$refs.response.skipPage(
            this.bidderFactor[this.selectNode.itemName]
          );
        }

        // 跳转到采购文件对应页码
        if (this.procurementShow && this.$refs.procurement) {
	        if (!this.procurementPdfRendered) {
		        this.$message.warning("采购文件正在渲染中，请稍候再试");
		        return;
	        }
					
          // 在对比模式下，采购文件应该跳转到采购文件的对应页码，而不是供应商的页码
          if (this.entDocProcurementPage && this.entDocProcurementPage[item.itemName]) {
            this.$refs.procurement.skipPage(this.entDocProcurementPage[item.itemName]);
          } else {
            // 在对比模式下，如果没有采购文件页码信息，则只跳转响应文件的页码，不跳转采购文件
            // 这样可以避免采购文件和响应文件显示不同的内容造成混淆
          }
        }

        // 获取当前项目的最大分数值，假设scoreLevel中第一个值为最大值（可根据实际规则调整）
        // const maxScore = item.score;
        // console.log("此项目最大分值是："+maxScore);
        // this.currentMaxScore = maxScore; // 将最大分数值存储到实例变量中，方便后续校验使用
      } else {
        this.$message.warning("请先选择供应商");
      }
    },
    initDefaultRatingArray(){
      Object.keys(this.defaultRatingArray).forEach((key) => {
        this.defaultRatingArray[key].state = null;
        this.defaultRatingArray[key].reason = "";
      });
    },

    /**
     * 校验所有评分项是否填写完整
     * @returns {boolean} 是否全部填写
     */
    validateAllRatings() {
      for (const item of this.scoringSystem.uitems) {
        const state = this.defaultRatingArray[item.entMethodItemId].state;

        // 评分结果未填写
        if (state === null || state === '' || state === undefined) {
          // this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);
          return true;
        }

        // 对于分数评分，检查是否为有效数值
        if (!item.scoreLevel || item.scoreLevel.length === 0 || item.scoreLevel === null || item.scoreLevel === undefined) {
          const score = parseFloat(state);
          if (isNaN(score) || score < 0) {
            this.$message.warning(`${item.itemName}的评分必须是有效的数值且不能小于0`);
            return false;
          }
          // 检查分数是否超过最大值
          const maxScore = this.getMaxScore(item);
          if (score > maxScore) {
            this.$message.warning(`${item.itemName}的评分不能超过${maxScore}分`);
            return false;
          }
        } else {
          // 对于有挡位的评分，检查是否在允许的挡位范围内
          const scoreLevels = item.scoreLevel.split(',').map(level => level.trim());
          if (!scoreLevels.includes(state.toString())) {
            this.$message.warning(`${item.itemName}的评分必须选择指定的挡位：${item.scoreLevel}`);
            return false;
          }
        }
      }
      return true;
    },

    tmpSave(){
      console.log("-------开始保存评审结果----------------");

      // 先校验所有评分项是否填写完整
      if (!this.validateAllRatings()) {
        return Promise.resolve({ code: 0, success: false }); // 校验失败
      }

      var ratingArray = JSON.parse(JSON.stringify(this.defaultRatingArray));

      var data = [];
      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {
        const item = this.scoringSystem.uitems[index];
        const itemId = item.entMethodItemId;
        // 获取当前项对应的评分结果
        const evaluationResult = ratingArray[itemId].state;
        // if (evaluationResult === null || evaluationResult === "") {
        //   // 如果评分结果为空，则不保存此条信息
        //   console.log("-------评分结果为空，不保存此条信息----------------");
        //   continue;
        // }
          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）
          const evaluationRemark = ratingArray[itemId].reason || "";
          data.push({
            scoringMethodUitemId: itemId,
            expertResultId: this.expertInfo.resultId,
            entId: this.selectSupplier.bidderId,
            evaluationResult: evaluationResult,
            evaluationRemark: evaluationRemark
          });
        }
        if(data.length>0){
            console.log("-------开始后台保存评审结果----------------");
            return scoringFactors(data).then((response) => {
              console.log(response.msg);
              if (response.code == 200) {
                this.$message.success("保存成功");
                return { code: 200, success: true };
              } else {
                this.$message.warning(response.msg);
                return { code: response.code, success: false };
              }
            }).catch((error) => {
              this.$message.error("保存失败");
              return { code: 0, success: false };
            });
        }else{
          return Promise.resolve({ code: 200, success: true }); // 没有数据需要保存时也返回成功
        }
    },
    save() {
      if (this.supplier == "") {
        this.$message.warning("请选择供应商");
      } else {
        //const data = this.generatingSavedData();
        var data = [];
        for (let index = 0; index < this.scoringSystem.uitems.length; index++) {
          const item = this.scoringSystem.uitems[index];
          const itemId = item.entMethodItemId;
          // 获取当前项对应的评分结果
          const evaluationResult = this.defaultRatingArray[itemId].state;
          // if (evaluationResult === null || evaluationResult === "") {
          //   // 如果评分结果为空，弹出提示，提示内容包含该项的itemName
          //   this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);
          //   return; // 直接返回，不再继续构建数据，等待用户填写完整
          // }
          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）
          const evaluationRemark = this.defaultRatingArray[itemId].reason || "";
          data.push({
            scoringMethodUitemId: itemId,
            expertResultId: this.expertInfo.resultId,
            entId: this.selectSupplier.bidderId,
            evaluationResult: evaluationResult,
            evaluationRemark: evaluationRemark
          });
        }

        scoringFactors(data).then((response) => {
          if (response.code == 200) {
            this.$message.success(response.msg);
          } else {
            this.$message.warning(response.msg);
          }
        });
      }
    },
    // 生成保存数据
    generatingSavedData() {
      var data = [];
      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {
        const item = this.scoringSystem.uitems[index];
        const itemId = item.entMethodItemId;
        // 获取当前项对应的评分结果
        const evaluationResult = this.defaultRatingArray[itemId].state;
        // if (evaluationResult === null || evaluationResult === "") {
        //   // 如果评分结果为空，弹出提示，提示内容包含该项的itemName
        //   this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);
        //   return; // 直接返回，不再继续构建数据，等待用户填写完整
        // }
        // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）
        const evaluationRemark = this.defaultRatingArray[itemId].reason || "";
        data.push({
          scoringMethodUitemId: itemId,
          expertResultId: this.expertInfo.resultId,
          entId: this.selectSupplier.bidderId,
          evaluationResult: evaluationResult,
          evaluationRemark: evaluationRemark
        });
      }

      /*for (let index = 0; index < this.scoringSystem.uitems.length; index++) {
        data.push({
          scoringMethodUitemId:
            this.scoringSystem.uitems[index].entMethodItemId,
          expertResultId: this.expertInfo.resultId,
          entId: this.selectSupplier.bidderId,
          evaluationResult:
            this.defaultRatingArray[
              this.scoringSystem.uitems[index].entMethodItemId
            ].state,
          evaluationRemark:
            this.defaultRatingArray[
              this.scoringSystem.uitems[index].entMethodItemId
            ].reason,
        });
      }*/
      return data;
    },
    submit() {
        this.tmpSave().then((saveResult) => {
          // 检查保存结果，如果校验失败则不继续提交
          if (!saveResult || saveResult.success === false) {
            return; // 校验失败，不继续提交流程
          }

          const data = {
            projectId: this.$route.query.projectId,
            expertResultId: this.expertInfo.resultId,
            scoringMethodItemId: this.$route.query.scoringMethodItemId,
          };
          checkReviewSummary(data).then((response) => {
            if (response.code == 200) {
            // 修改专家进度
            const status = {
              evalExpertScoreInfoId: JSON.parse(
                localStorage.getItem("evalExpertScoreInfo")
              ).evalExpertScoreInfoId,
              evalState: 1,
            };
            editEvalExpertScoreInfo(status).then((res) => {
              if (res.code == 200) {
                this.$message.success("提交成功");
              }
            });
            this.$emit("send", "two");
          } else {
            this.$message.warning(response.msg);
          }
        });
      })
    },
    /**
     * 显示采购文件PDF
     */
    viewPurchasing() {
      this.activeButton = 'procurement'; // 设置当前激活按钮
      this.double = false; // 单文件模式
      this.responseShow = false; // 不显示响应文件
      this.procurementShow = true; // 显示采购文件
      this.isPdfRendering = true;

      // 设置采购文件PDF地址
      if (this.file.tenderNoticeFilePath) {
        this.procurementPdf = this.file.tenderNoticeFilePath;
      }

      // 右侧评分项显示为采购文件的评分项
      let pageProcurementArr = []; // 采购文件评分项数组
      for (let item in this.entDocProcurementPage){
        pageProcurementArr.push({
          itemName: item,
          jumpToPage: this.entDocProcurementPage[item]
        })
      }

      console.log(this.scoringSystem.uitems);
      console.log(pageProcurementArr)
      this.pageProcurement = [];
      for (let i = 0; i < this.scoringSystem.uitems.length;i++){
        for (let j = 0; j < pageProcurementArr.length;j++){
          if (this.scoringSystem.uitems[i].itemName == pageProcurementArr[j].itemName){
            this.pageProcurement.push({...this.scoringSystem.uitems[i],...pageProcurementArr[j]});
          }
        }
      }
      console.log(this.pageProcurement)
    },

    /**
     * 跳转到采购文件对应页码
     * @param {Object} item - 评分项对象
     */
    jumpToProcurementPage(item) {
      if (item.jumpToPage && this.$refs.procurement) {
        this.$refs.procurement.skipPage(item.jumpToPage);
      }
    },
	  
	  /**
	   * 检查是否可以跳转页面
	   * @returns {boolean} 是否可以跳转
	   */
	  canJumpToPage() {
		  // 如果只显示采购文件
		  if (this.procurementShow && !this.responseShow) {
			  return this.procurementPdfRendered;
		  }
		  // 如果只显示响应文件
		  if (this.responseShow && !this.procurementShow) {
			  return this.responsePdfRendered;
		  }
		  // 如果对比模式（两个都显示）
		  if (this.responseShow && this.procurementShow) {
			  return this.responsePdfRendered && this.procurementPdfRendered;
		  }
		  return false;
	  },
	  /**
	   * 处理PDF渲染状态变化
	   * @param {boolean} isRendered 是否渲染完成
	   * @param {string} pdfType PDF类型：'response' 或 'procurement'
	   */
	  handlePdfRenderStatusChange(isRendered, pdfType) {
	   if (pdfType === 'response') {
	    this.responsePdfRendered = isRendered;
	   } else if (pdfType === 'procurement') {
	    this.procurementPdfRendered = isRendered;
	   }
	   
	   // 当所有正在显示的PDF都渲染完成时，解除禁用状态
	   let allRendered = true;
	   if (this.responseShow && !this.responsePdfRendered) {
	    allRendered = false;
	   }
	   if (this.procurementShow && !this.procurementPdfRendered) {
	    allRendered = false;
	   }
	   
	   if (allRendered) {
	    this.isPdfRendering = false;
	   }
	   
	   if (isRendered) {
	    console.log(`${pdfType === 'response' ? '响应' : '采购'}文件渲染完成，可以进行页面跳转`);
	   }
	  },
	  
    // 跳转到二次报价
    secondOffer() {
      const query = {
        projectId: this.$route.query.projectId,
        zjhm: this.$route.query.zjhm,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("tenderOfferScoringMethodItems")
        ),
      };
      this.$router.push({ path: "/secondOffer", query: query });
    },
    // 跳转到询标
    bidInquiry() {
      const query = {
        projectId: this.$route.query.projectId,
        zjhm: this.$route.query.zjhm,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("tenderOfferScoringMethodItems")
        ),
      };
      console.log("query", query);
      this.$router.push({ path: "/bidInquiry", query: query });
    },
    // 获取因素对应页码
    getFactorsPage() {
      this.factorsPage = JSON.parse(localStorage.getItem("entDocResponsePage"));
    },
    
    /**
     * 获取评分项的最大分值
     * @param {Object} item - 评分项对象
     * @returns {number} 最大分值
     */
    getMaxScore(item) {
      if (!item) return 0;
      
      // 如果有分数挡位，使用挡位中的最大值
      if (item.scoreLevel && item.scoreLevel.length > 0 && item.scoreLevel !== null && item.scoreLevel !== undefined) {
        const scoreLevels = item.scoreLevel.split(',').map(level => parseFloat(level.trim())).filter(level => !isNaN(level));
        if (scoreLevels.length > 0) {
          return Math.max(...scoreLevels);
        }
      }
      
      // 否则使用配置的最大分值
      return parseFloat(item.score) || 0;
    },
	  downloadFile(item){
			this.$download.zip(item.filePath,item.fileName);
	  },
	  
	  // ========== 悬停相关 ==========
	  /**
	   * 显示评分项悬浮框
	   * @param {Object} factorItem 评分项对象
	   */
	  showFactorTooltip(factorItem) {
		  if (!factorItem.itemRemark) return; // 如果没有评审内容则不显示
		  
		  // 清除之前的定时器
		  if (this.tooltipTimer) {
			  clearTimeout(this.tooltipTimer);
		  }
		  
		  // 延迟显示悬浮框，避免快速移动时频繁显示
		  this.tooltipTimer = setTimeout(() => {
			  this.hoveredFactorNode = factorItem;
		  }, 300); // 300ms延迟
	  },
	  
	  /**
	   * 隐藏评分项悬浮框
	   */
	  hideFactorTooltip() {
		  // 清除定时器
		  if (this.tooltipTimer) {
			  clearTimeout(this.tooltipTimer);
			  this.tooltipTimer = null;
		  }
		  
		  // 延迟隐藏，给用户时间移动到悬浮框上
		  setTimeout(() => {
			  this.hoveredFactorNode = null;
		  }, 100);
	  },
	  
	  /**
	   * 清除悬浮框定时器（当鼠标移动到悬浮框上时）
	   */
	  clearTooltipTimer() {
		  if (this.tooltipTimer) {
			  clearTimeout(this.tooltipTimer);
			  this.tooltipTimer = null;
		  }
	  }
  },
  mounted() {
    this.init();
    this.getFactorsPage();
  },
	beforeDestroy() {
		// 清理定时器
		if (this.tooltipTimer) {
			clearTimeout(this.tooltipTimer);
			this.tooltipTimer = null;
		}
	},
};
</script>

<style lang="scss" scoped>
// Main layout containers
.technical-review-container {
  min-height: 57vh;
  display: flex;
}

.main-content {
  min-height: 57vh;
  width: 79%;
}

// Header section styles
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #176ADB;
  padding: 15px 20px;
}

.title-section {
  display: flex;
  height: 36px;
  font-weight: 700;
  font-size: 24px;
  color: #333333;
}

.main-title {
  // Inherits from title-section
}

.help-section {
  display: grid;
  justify-items: center;
  position: relative;
  bottom: -30px;
}

.help-text {
  font-size: 12px;
}

.help-image {
  width: 80px;
  height: 30px;
  margin-right: 20px;
}

// File list container
.file-list-container {
  border-right: 1px solid #e6e6e6;
  border-left: 1px solid #e6e6e6;
  padding: 10px;
  overflow-y: auto;
	gap: 20px;
	flex: 1;
	flex-wrap: wrap;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;
	::v-deep .el-card__body {
		padding: 0;
	}
}

.file-list-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.file-item {
  margin-bottom: 8px;
  cursor: pointer;
}

.file-item-content {
  display: flex;
  align-items: center;
  padding: 5px;
}

.file-icon {
  margin-right: 8px;
  color: #409EFF;
}

.file-name {
  font-size: 12px;
  flex: 1;
  word-break: break-all;
}

.download-icon {
  margin-left: 8px;
  color: #999;
}

// Action buttons section
.action-buttons {
  text-align: right;
}

.button-group {
  margin-top: 20px;
	.item-button{
		color: #fff;
	}
}

.primary-btn {
  background-color: #176ADB;
  color: #FFFFFF;
  border: 1px solid #176ADB;
}

// Content section styles
.content-section {
  display: flex;
  height: 82%;
}

// PDF preview area
.pdf-preview-area {
  display: flex;
  justify-content: center;
  flex: 1;
}

.pdf-panel {
  width: 49%;
}

.response-panel {
  &.border-right {
    border-right: 1px solid #176ADB;
  }
}

.procurement-panel {
  &.border-left {
    border-left: 1px solid #176ADB;
  }
}

// Divider styles
.divider {
  min-height: 57vh;
  width: 1%;
  background-color: #F5F5F5;
}

// Sidebar styles
.sidebar {
  min-height: 57vh;
  width: 20%;
}

.sidebar-header {
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 2px solid #176ADB;
  padding: 15px 20px;
}

.supplier-select {
  width: 100%;
}

.sidebar-content {
  padding: 15px 20px;
}

// Factor items styles
.factor-item {
  margin-bottom: 10px;
}

.factor-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  text-align: left;
  width: 98%;
}

.factor-name {
  cursor: pointer;
  font-family: SourceHanSansSC-Bold;
  font-weight: 500;
  font-size: 16px;
  color: #333333;
  letter-spacing: 0;
}

.max-score {
  font-size: 12px;
  color: red;
}

.factor-input {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  padding: 10px;
}

.score-value {
  color: green;
  font-size: 16px;
}

// Submit section
.submit-section {
  display: flex;
  margin: 32px 0;
  justify-content: space-evenly;
}

// Review content styles
.review-content {
  text-align: left;
  font-size: 14px;
}

.review-title {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 15px;
  color: #176ADB;
  letter-spacing: 0;
}

.review-text {
  padding: 6px 30px;
}

// Existing styles
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
	  transition: all 0.3s ease;
	  padding: 4px 8px;
	  border-radius: 4px;
	  
	  &:hover {
		  background-color: #f0f8ff;
		  color: #176ADB;
		  transform: translateX(2px);
	  }
  }
}
.item-button {
  border: 1px solid #979797;
  width: 150px;
  height: 36px;
  margin: 0 10px;
  font-weight: 700;
  font-size: 17px;
  border-radius: 6px;
  color: #333333;
  &:hover {
    color: #333333;
  }
}
.technical-blue-btn {
	background-color: #fff !important;
	color: #333 !important;
	border: 1px solid #979797 !important;
}
.technical-blue-btn-active {
	background-color: #176ADB !important;
	color: #fff !important;
	border: 1px solid #176ADB !important;
}
.item-button-little {
  width: 124px;
  height: 36px;
  font-weight: 700;
  font-size: 18px;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
.factors {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 10px;
}
.text {
  ::v-deep .el-textarea__inner {
    background-color: #f5f5f5;
    border-radius: 0;
    border: 1px solid #f5f5f5;
  }
}

.fileList {
	display: flex;
	align-items: center;
	gap: 20px;
	flex: 1;
	flex-wrap: wrap;
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  ::v-deep .el-card__body {
    padding: 0;
  }
}

// PDF渲染状态提示样式
.render-status-tip {
	display: flex;
	align-items: center;
	padding: 10px 15px;
	margin-bottom: 15px;
	border-radius: 4px;
	background-color: #fff7e6;
	border: 1px solid #ffd591;
	color: #d48806;
	font-size: 14px;
	
	i {
		margin-right: 8px;
		font-size: 16px;
	}
	
	&.success {
		background-color: #f6ffed;
		border-color: #b7eb8f;
		color: #52c41a;
	}
}

// 禁用状态的评分项标题样式
.factor-title.disabled {
	color: #999 !important;
	cursor: not-allowed !important;
	opacity: 0.6;
	
	&:hover {
		color: #999 !important;
	}
}

// 悬浮框样式
.factor-tooltip {
	position: absolute;
	right: 100%; /* 显示在父元素左侧 */
	top: 0;
	margin-right: 10px; /* 与评分项的间距 */
	background: #fff;
	border: 1px solid #e4e7ed;
	border-radius: 8px;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
	width: 400px;
	max-height: 300px;
	overflow: hidden;
	z-index: 9999;
	
	.tooltip-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 12px 16px;
		background-color: #f5f7fa;
		border-bottom: 1px solid #e4e7ed;
		
		.tooltip-title {
			font-weight: 600;
			font-size: 14px;
			color: #176ADB;
		}
		
		.tooltip-close {
			cursor: pointer;
			color: #909399;
			font-size: 14px;
			
			&:hover {
				color: #176ADB;
			}
		}
	}
	
	.tooltip-content {
		padding: 16px;
		font-size: 14px;
		line-height: 1.6;
		color: #333;
		max-height: 240px;
		overflow-y: auto;
		
		// 美化滚动条
		&::-webkit-scrollbar {
			width: 6px;
		}
		
		&::-webkit-scrollbar-track {
			background: #f1f1f1;
			border-radius: 3px;
		}
		
		&::-webkit-scrollbar-thumb {
			background: #c1c1c1;
			border-radius: 3px;
			
			&:hover {
				background: #a8a8a8;
			}
		}
	}
}

// 评分项容器相对定位
.factor-item {
position: relative;
}

// PDF渲染期间禁用状态样式
.item-button:disabled {
opacity: 0.6 !important;
cursor: not-allowed !important;
background-color: #f5f5f5 !important;
color: #c0c4cc !important;
border-color: #e4e7ed !important;
}

.el-select.is-disabled .el-input__inner {
background-color: #f5f5f5;
border-color: #e4e7ed;
color: #c0c4cc;
cursor: not-allowed;
}

.fileListInner{
	display: flex;
	gap: 20px;
	flex-wrap: wrap;
}
</style>

